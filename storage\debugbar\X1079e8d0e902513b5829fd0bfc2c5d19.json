{"__meta": {"id": "X1079e8d0e902513b5829fd0bfc2c5d19", "datetime": "2025-07-30 09:57:27", "utime": **********.60684, "method": "GET", "uri": "/finance/sales/contacts/lead/11", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753869446.706744, "end": **********.606869, "duration": 0.9001250267028809, "duration_str": "900ms", "measures": [{"label": "Booting", "start": 1753869446.706744, "relative_start": 0, "end": **********.483995, "relative_end": **********.483995, "duration": 0.7772510051727295, "duration_str": "777ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.484008, "relative_start": 0.7772641181945801, "end": **********.606872, "relative_end": 3.0994415283203125e-06, "duration": 0.1228640079498291, "duration_str": "123ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46688872, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=997\" onclick=\"\">app/Http/Controllers/FinanceController.php:997-1056</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.014020000000000001, "accumulated_duration_str": "14.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.547563, "duration": 0.011380000000000001, "duration_str": "11.38ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 81.17}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.576671, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 81.17, "width_percent": 8.131}, {"sql": "select * from `leads` where `id` = '11' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["11", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1023}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5829442, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1023", "source": "app/Http/Controllers/FinanceController.php:1023", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1023", "ajax": false, "filename": "FinanceController.php", "line": "1023"}, "connection": "radhe_same", "start_percent": 89.301, "width_percent": 10.699}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/11", "status_code": "<pre class=sf-dump id=sf-dump-1021569550 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1021569550\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1168523477 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1168523477\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2121659664 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2121659664\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-624963560 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjF2R2MyU01SYXN4R1pPaDd3cUQwd2c9PSIsInZhbHVlIjoiK2VaVWpaRHJpVUViTThZNUsrWkkwa3BvM0plZ04yOUpoeXFLaG9PeDZhWFE5aG9wZzkxN3FaUkR5d1pETkovQ2p2emNrWDNna3IxY1Ruc1NBUFB4amREYSt2RnkzZmwrblI5NDFuU202Q2VtVUptK2xlbmhMRm1uTWV4V0dyeXlkdTB6b2FucE1uNURsYkNEWEwvcHpocXNyUTIwa3VNMC9yZ01ZRFFVYXNjcnVUemtETXdlTEptNy9GVERsZ3lHbGFabU9ybnVSU1kvR0dKUk1KK1VZdURsN3JWT2JYSm1LMXA3cDBIM0RiTjB2R3E4Z3R1bDFvNlVQM3k2SjR5Y1N0dDZURktTODg3SUtOR0dTUE5vN2F6dTFGc3Rtb2lRbTgyT1E1S0RCT2ZBZnYvOURKdlNYSnpObTlEWm9zbnFOTVhENTdsampnQ0plRFo3L01pVHZ2OWQ4RnFyd1ZQd2xvNko1OEZobGJXZVFsbjVvK21EWmlVTkVxSEdvbTJnUnVyU3Rramw2ZzRlSFBMQkQ0NTB0T2grbVZnenR3Z3pOR3ZnTU5XVlluYmV0T0ZaWXhyZDE1ajNUYkI2QVZkdVNYclpwelFqOCtOM0pnNDh6SXdzaUtpaUorTW1ocmFmSWNCR3d4VjN0cnVJQjl2eFNNdlNCaVdBV0pMNWRwL1QiLCJtYWMiOiJmYmVjOWM3NmE3M2MwOGYxMDQ3NjIyNGY4NjI0ZWUyYmJhOWM4NDllY2FiOWFhNzY1ZTg2NmU4OWQwZWQzZmYzIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImZGeHNHOTUzNm9xODNpUDh4TGY1dUE9PSIsInZhbHVlIjoiWUFsNTVyUklNZUhBMndOVVJkaWlRQkRhZFFXYVVyR21DQVk2dUdoQnBiMVZBcWF0LzZOYWxkRWFGdUh3YXdPVy9PdEIxeURQVFNGRXZWOWZDREF2M0VWVDd2NjlnakIzZzEwVWVQSlNPTzBJeUpaZmVVaFN0S204RXlTMUlqaDBpSEI5VUF6V3hLdkQ2UmhVQ09PL2RCNXhVZ3RxRDVHRnZJOGsyNUliWmFpWnAxK3pCb1BIUXg4ejVrNlBwK2dzejBmQ0NSUTZOc1ZtMldzTE9RRUo3T2xoV3h6aXpCNE8vdHFhSUxoTDR3em5UcURUbGlBOCtvanp2V0F5SnlvWjd0bXU4Mk5MMWVvR09JaVg0MlREN2Z2M01mTUFGNVRseXZJUG93WkhOQUFPckh2TFl6T2svcUZEWjJyaG5DU21WcWY1RXlWSXgwRjFYQzd0YVo1RDZ5anpBNFJmZEVQUSt1bVN4Q1Y5V2k1WnRmOTBVU25RSmJwbFNOU0M0eWdZcGYxQnNGMDgxYUFzZGpjaUVvcFNleWtzR2Z0TThMdG1mRnFNTTAvdVpyZEg0c09JSm1CZ2FwWE9QMXJ4TFY5Um5vVWN2WlhrUGw1NjF0V0t4eVJrUFZhTXRYT0FuazV3aGIyaXAyZHZzQWVkRFEwS1o4QlVFTldBRHJjVEEvVWkiLCJtYWMiOiI2YjNjNGRkMzAyM2RlNzZiMzk0N2EyMzIzZTVmMjliZjYyMzM1OTYxNmQ3YTgwNzM1YjY3N2FhZDc4ZTMyZWU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-624963560\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-272552219 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-272552219\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-759672865 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:57:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZHcEZtaFlDeG9KNG80NzFOSExFN1E9PSIsInZhbHVlIjoiTGJVRnNpM1V6NXlmTkVwbjFiN0lUQVQwdkRzaFIreEpJVlpDL21EdUFEb2JIQlMxQ1dkYjZjbjV3cENaN2ROYUdzS0M2VVlEY0ZOUnlKc2hvMUxxVEtZR3BwZ05Uc0FybmhSWGt4d01HcXQ5aURQYXhpNW5kVGcydkZlZFhGOFh6ZEQ3WUVNNGFib3J1cXYvZWY0amJVTG9ZZ1JhbnhiWmZucGlSMW9XQkhaUTM4UVpZamxnTE5uWEVvdGNVOUJ3dmUzbU9JVnhrUGVlNFREdTdZNWdmbHF5VEg4N1BtL21WcEJ1Nk5HeFVkZGs3MldHREpnL24vK0gxM2c0Qnd3eWF6RGNyaENpcEo4ak4wYzBncWhCcVlIdTdUN1NCT3pmRDZ5dGVXZXp4SWN2Ui9BQjIxZUNrblp2dUVMRVA3QWFDYWhQdXdTS01tdi9rSy9VV21uMExUdTA2YnYzV0ozVXc3Z3F1OUptdFh6ZDRldlliSUpINGIyRnhZblc1TlhSbnFZeFZXLzdBd2pLWm10OEVYaW5DRnJERXpIN2M4Ym9hVi8ramZjeHJJQStPWHFpeGpyT1p1b3gyQjV5bDZmVjNwemZrL043NXNxVlM3TXZPbnZQZUM1MjZrVVRkS29iVUFrTWlRWTFDaEd5WUE2akQzZnowSmNiWVVNaGV4L1ciLCJtYWMiOiJiMDYzYjVmYjdlMzQwMTRkOThlZTc1M2U0MjM5MDIzMWQzMzgyOGJmMDQ3ZDIyZDE3OTU2OTY1NGRjOWQyYjM5IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:57:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjhKN2duNmEzTUJUUG9qWHdWWHNiWlE9PSIsInZhbHVlIjoiNENjZXJWSjM0VFMyemYwd1NQbjU2YVdLdkNSbFBqT1E4em5Dck9ZS3ozL1hwQkVmYlVIZGgwUEdIZkZIN3ZISzFUSEJQcWY5U1lJVSthNm9sTDZsU0Z6NUdQcklFMjZJUlRiKzVBNHlBaXFVOWw4RVE4N1FvVE1UcElyMmlyRTRHTEdtTjVYSG9DUlhQdEYvU0VHR3ZyWm05YlNtMHoyMUIydGlCdjlZY0tFb2oyS1pMSm1RWUFSRXhRb1NMMEQ0N05QVHorUEl2alViZVluVk03V2pkOHlHSHNYOVhZaEQ0VDUydSttRy9neUd3NDEwTTdmSFppdjdVMlpzSVg4bEkrc0ltVGxnQ2ZlSGxBaU1PN3kvby9ncmRCWGErVGVkcE9pV2R2NTF3SUQrNDRmcW40K2lvaWxueHNnOFZtcUdxUTBNbm8rTWk1azZzQThnVFFQK21TQThic3cyVXRlcU9yUjU0cHM2QmJFVmJsdEVvYUsxMjdtT2RnOWJweWd0ZjNUZCtJSGJXNyt0cUt2cFVIYVkzc0dwK2g2U0ZtUVBWQVVJeUZ6YUVzVDVPVjhPRUdEQTJNdXpIMkcvb2I4dUx0STVLSk9XelN5Qms1UW4xVHFpZHg1S1g4ekZIV2tQMzVTOU1uWXV3R3k0d3pmdTREUmZWVU44c0RZRDhWSWsiLCJtYWMiOiI3YjVlNjllMjMzNGNkZWU4YzQwZWY4ODU3ZTViNjA2NTQ5MDI0ZDE2YzU4MjQ0YWEyNjBmZjNlMjRjOTQyYTcwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:57:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZHcEZtaFlDeG9KNG80NzFOSExFN1E9PSIsInZhbHVlIjoiTGJVRnNpM1V6NXlmTkVwbjFiN0lUQVQwdkRzaFIreEpJVlpDL21EdUFEb2JIQlMxQ1dkYjZjbjV3cENaN2ROYUdzS0M2VVlEY0ZOUnlKc2hvMUxxVEtZR3BwZ05Uc0FybmhSWGt4d01HcXQ5aURQYXhpNW5kVGcydkZlZFhGOFh6ZEQ3WUVNNGFib3J1cXYvZWY0amJVTG9ZZ1JhbnhiWmZucGlSMW9XQkhaUTM4UVpZamxnTE5uWEVvdGNVOUJ3dmUzbU9JVnhrUGVlNFREdTdZNWdmbHF5VEg4N1BtL21WcEJ1Nk5HeFVkZGs3MldHREpnL24vK0gxM2c0Qnd3eWF6RGNyaENpcEo4ak4wYzBncWhCcVlIdTdUN1NCT3pmRDZ5dGVXZXp4SWN2Ui9BQjIxZUNrblp2dUVMRVA3QWFDYWhQdXdTS01tdi9rSy9VV21uMExUdTA2YnYzV0ozVXc3Z3F1OUptdFh6ZDRldlliSUpINGIyRnhZblc1TlhSbnFZeFZXLzdBd2pLWm10OEVYaW5DRnJERXpIN2M4Ym9hVi8ramZjeHJJQStPWHFpeGpyT1p1b3gyQjV5bDZmVjNwemZrL043NXNxVlM3TXZPbnZQZUM1MjZrVVRkS29iVUFrTWlRWTFDaEd5WUE2akQzZnowSmNiWVVNaGV4L1ciLCJtYWMiOiJiMDYzYjVmYjdlMzQwMTRkOThlZTc1M2U0MjM5MDIzMWQzMzgyOGJmMDQ3ZDIyZDE3OTU2OTY1NGRjOWQyYjM5IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:57:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjhKN2duNmEzTUJUUG9qWHdWWHNiWlE9PSIsInZhbHVlIjoiNENjZXJWSjM0VFMyemYwd1NQbjU2YVdLdkNSbFBqT1E4em5Dck9ZS3ozL1hwQkVmYlVIZGgwUEdIZkZIN3ZISzFUSEJQcWY5U1lJVSthNm9sTDZsU0Z6NUdQcklFMjZJUlRiKzVBNHlBaXFVOWw4RVE4N1FvVE1UcElyMmlyRTRHTEdtTjVYSG9DUlhQdEYvU0VHR3ZyWm05YlNtMHoyMUIydGlCdjlZY0tFb2oyS1pMSm1RWUFSRXhRb1NMMEQ0N05QVHorUEl2alViZVluVk03V2pkOHlHSHNYOVhZaEQ0VDUydSttRy9neUd3NDEwTTdmSFppdjdVMlpzSVg4bEkrc0ltVGxnQ2ZlSGxBaU1PN3kvby9ncmRCWGErVGVkcE9pV2R2NTF3SUQrNDRmcW40K2lvaWxueHNnOFZtcUdxUTBNbm8rTWk1azZzQThnVFFQK21TQThic3cyVXRlcU9yUjU0cHM2QmJFVmJsdEVvYUsxMjdtT2RnOWJweWd0ZjNUZCtJSGJXNyt0cUt2cFVIYVkzc0dwK2g2U0ZtUVBWQVVJeUZ6YUVzVDVPVjhPRUdEQTJNdXpIMkcvb2I4dUx0STVLSk9XelN5Qms1UW4xVHFpZHg1S1g4ekZIV2tQMzVTOU1uWXV3R3k0d3pmdTREUmZWVU44c0RZRDhWSWsiLCJtYWMiOiI3YjVlNjllMjMzNGNkZWU4YzQwZWY4ODU3ZTViNjA2NTQ5MDI0ZDE2YzU4MjQ0YWEyNjBmZjNlMjRjOTQyYTcwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:57:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-759672865\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1323128193 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1323128193\", {\"maxDepth\":0})</script>\n"}}