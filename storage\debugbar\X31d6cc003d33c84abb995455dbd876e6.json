{"__meta": {"id": "X31d6cc003d33c84abb995455dbd876e6", "datetime": "2025-07-30 10:41:24", "utime": **********.31148, "method": "GET", "uri": "/omx-new-saas/login", "ip": "::1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753872083.010233, "end": **********.311507, "duration": 1.301274061203003, "duration_str": "1.3s", "measures": [{"label": "Booting", "start": 1753872083.010233, "relative_start": 0, "end": 1753872083.97858, "relative_end": 1753872083.97858, "duration": 0.9683470726013184, "duration_str": "968ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753872083.978597, "relative_start": 0.9683640003204346, "end": **********.31151, "relative_end": 3.0994415283203125e-06, "duration": 0.3329131603240967, "duration_str": "333ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46666984, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.193233, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.211486, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.286888, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=263\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:263-278</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.08358000000000002, "accumulated_duration_str": "83.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 545}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 267}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0521939, "duration": 0.02284, "duration_str": "22.84ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 27.327}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'radhe_same' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 527}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 270}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.086622, "duration": 0.05026, "duration_str": "50.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:527", "source": "app/Models/Utility.php:527", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=527", "ajax": false, "filename": "Utility.php", "line": "527"}, "connection": "radhe_same", "start_percent": 27.327, "width_percent": 60.134}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 533}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 270}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.145902, "duration": 0.00267, "duration_str": "2.67ms", "memory": 0, "memory_str": null, "filename": "Utility.php:533", "source": "app/Models/Utility.php:533", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=533", "ajax": false, "filename": "Utility.php", "line": "533"}, "connection": "radhe_same", "start_percent": 87.461, "width_percent": 3.195}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.1944911, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 90.656, "width_percent": 0.969}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.212929, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 91.625, "width_percent": 1.316}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3940}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3988}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.252013, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3940", "source": "app/Models/Utility.php:3940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3940", "ajax": false, "filename": "Utility.php", "line": "3940"}, "connection": "radhe_same", "start_percent": 92.941, "width_percent": 2.812}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3943}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3988}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 9}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.2675622, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3943", "source": "app/Models/Utility.php:3943", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3943", "ajax": false, "filename": "Utility.php", "line": "3943"}, "connection": "radhe_same", "start_percent": 95.753, "width_percent": 1.376}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.274966, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 97.128, "width_percent": 2.872}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "5bAS6pxlTjQQmoANE7jULrxXjNgNlmc1m9mk4gE8", "url": "array:1 [\n  \"intended\" => \"http://localhost/omx-new-saas/finance/sales\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/omx-new-saas/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-465606464 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-465606464\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-661148575 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-661148575\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1125260421 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1125260421\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1118233734 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6InJySUlYaFk0MFVUb3lVdVpKS2RHMHc9PSIsInZhbHVlIjoiS1E2VlZ0N2VzeXVKWmN2RFRQT25lWFNnZGxoMWVHb3pZaStzQ2lnK2ljNnM3MStSZk94b1lHRTZFajliYUhLdnJnaktnL1JPVFRaYmVEcU4rMWcrSWZzVjNMZ3hoTGN2eFBHZkxxbHlSS3MyNFlmSFQwSUxBa1pRR0FzVEFPR1lla3pjMWZDY2I2ZzRDN3dZZ0pWUTMyQXl4SGJ4dmNVRi9kbVdGQ1VWV3NSRWtPZTQ1cXdjRjhBbEN6UjhvZnRkZUdIMk5BLzVKK0pzbGVHZ1M0aUZnbWc5czNaMlM0ZHBpQnRnQnhkM1dWQTl3NXVqQnd2RVVRa0NSdGp4cVZjMHZJbGhWK2M1UFJwNEU3QkUwSlVmNWlST21XUGxTM1Nvdk5obXJNMjA0dHhxdjY0eElTTVFWdFc3amtWMXIrSUVoc0lyMW5aeHo0endFVzNnU1U1bUZXVTQ2elJNejZqQkdHSTBnS3VjWG5CWXU2UWJZTzJMZkdYcmRZb1BhTHhnT1c5NTBmVTlzSlhZRXVJN0h4SzZwUFQ0eTR5aUR2NzF0Z2V5M1c3WVBUVzczMFpDN0Vsbm5hNU1SVXIyaWx1NFRZdVV5TTd2YmNhdDVNeEhWZ011ZVBLTUFsZ2hxWjF6ampwenZHa3FBY1VwMG05YUV5dGtlTHpuTlBrUEk1dDUiLCJtYWMiOiJiMzgyYmI4MzFkY2ZlODFhMDBlOWQwMmM3OTY5MTJjZDU5NTk2MjQ1OTczNWZkZjEyMjQwYTUwNWRmODgwYjU3IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlFmaTY0M3g2enNoYk5mV1J5MDhXcUE9PSIsInZhbHVlIjoiT0JibGsvRjl1V1hvVXZZeUVHQzlmK0FRb1Vmc21BVWlzY1hhdEsyUnozY1NwVzV0YmhUN0J2NEZDVG9CYUlyalM2S2JQSUFkcEtrNXU4ZFM3VitnbGJqMEJrRmVYcDA2R1FIMzd1NGFLazNSa0lBS1NOa3VkeTdBOGVnalJkdFFMdmZVamg1Nk8vVk9ObmN2d3ptbjBORVEzdTBQZGIvOWtjaTBqWGRmeEQrQjZOR3BkeGRzQ2U0dC82TVhOaW9rRzNCcWpKL1VuMUI2Z0JkYzFXaW0rOVZFRWZHa2J2M0NpemxQaGhPSWtaQ0FhSDJCcWV1Mk9RL0RPRkRvWTBpL2pPcThNUjJRWFkyVlNNK3lKMDI5RG5LOTdPVFNzZCtHSEt0Z2cxcUZKVmpOb0JEM011NGJ3QXpVcFNUT05pSDhia2ZnRjRrT1hqU0FXalNweE83Wi9uTnl5MWVGd3h1ZEE3eUZYU3h5dTZHeW5zT09Ccld4T1h5WlFUWTgrK0ZyTHNQVW9KcndKbFpITHlnK0V6YzI2WlR4RU5hK21HMlZaNzZtUjFLWDVrVzRZM1dkRElsRjF1bi80bWN5aDJCRVlpQ1dmdTRJVGpjeG9NZm82ekwxSytnMkVMR2ZmYXRkSExDMGEzY29iQnlDQysyTzA0TWs0SXpCQnJJb1VHNGsiLCJtYWMiOiI3ZTE5Nzc1MjI4NzE2MWU0NjVmMmJhYTA4NWEwMTY4MTE1YWViOWRlZGQ3NjNhNDU3ZDU2ZThkNzVlMjJkMjY5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1118233734\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1023290190 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5bAS6pxlTjQQmoANE7jULrxXjNgNlmc1m9mk4gE8</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7f4EUZP93e4zKShJdjMEQKdIg0VLTOjvnrPmQ2oa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1023290190\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1870821 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 10:41:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik8xY052ZTkxVVEyZXZDc1Q3V3R1MWc9PSIsInZhbHVlIjoidUxqOFpvSlhUNThCalpreE1ORWtiUTBTcmxXcFZTUnJkNjlMc1JTeENuSXhoK3JlVldIUE1oMjZBRWJZdStwbkp4SVhwZldIbnl4ZWFKb3FPZkFEUy9pVlNHa0M0d1lWem54akxjaytjZzM2OEVUemp6eFM0eUxCSlk0T1lkdlZwam1UdGM4Tkd2SWk5S3FENGp0ZW9yYkFzRU05OXl2M0RUdWhEZ2dueFpDZUZNYnhYb2p1aENoZVFmeldEQXZMVnpEUGNGOTNQVVAyVGlyRUlUc2dOcTUybXh2NUFnK3RORHdDL2s4ZTkvaVlNaWV0d2tqdXNrc1M0Rkp6SVJvenFBeWE0T0ZKWFBlTVF3WE13UG9qdm9XYmNUOWdaY1I3MEVadWZFNUE4clVSWFpuT20xcmVKZitiRC82NHNwSXIwZ3BZbnh0c1ZqQVZHdE5WclgvRzhnUTBmc0ZmdkxpZXVHc1p5ZzV3ZG9EaFM1QnVUWU5LTFVrT2wyZk1jZzJDVE1WNHJmaEtMYnNDV09yN0FZc1BKVDJaTElTOHkzWjViQVJPTTRXTDVBaGZodURrK2lNZEY3c1JJeEJBWGNCOHRTNjhDZEVTbVdjTGdGMDNmRUVuTm4zU2laUXJhOFIxYWx4VDVmRFBUNVdySjJFMnErelh2L2Z2QXdFZHllanciLCJtYWMiOiIwYWZjMWU3NGY5MDg5MmNmMGM0MDE2OTk0ODliNzk1ODlmYTc1OTMwODg1MjczNjgxYmY2NmI0NjM0Y2E2Yzk0IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:41:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InphcEhUYUkzNit4MXlZYmZ1WUl5QXc9PSIsInZhbHVlIjoiUVpyMkI5Q2JSWWl4Z1hLRkcyMXpHTllpRml1Y1ROckxPcmo3WnRiME9uV2FZSGNLandpb0p6Unk3eW1mcjJjTXhUb01BTCtFb2NNWTVaTloxSXRhM01leFI5WjVHdHhKanJiSm9mV0hBTTh0d2h4Rm9jcDNBMG5MRE1samNqb1pqK085YkI5TWg0cDVwdGd4aDlKakRCSEJYU0VKcFJ6VHZ2S0ZCNXJxWTlMenlmZEIvWi9OU3hkZkZ6ZVVIdzRzbHJocDMxUU9uZjdsbmlzOHhXUEx4c3NZSUkvVml1cGdOK08zUHN2K3VDcTVjQnBUWWpJVW83c1o4Uk8vSURwbjlIKyt2UWxvU1J4L011K3RyaGhHYjI3WFBLK3NFYWg1NW4zTUduaWZXdFJ2NmRmZDIraHZZUXhxMllHRHNNVWEycW53NnIzc2Y4OWRkaHBaTlVWeVZvTGZZZFkxNTRXalpsMmFUY1VCTnlTaklub0hKbUg5c2VWSlphSEJha2pJTjhzZlBEM3RITEZyZ1R0SWwvcTZMb2xUYWtFbmh4eFJhNTBBbjl5eUJrRUFzQmpZSnYwQ2VBSENnY2NHeWlsU0FIUDRmQWNmRlZXV2FPOWdWNHp6WjRFaDVmZkt0Wmh0bGN6Ny9TYjYyVzJOVGx5MFZ1R0pxL2tRaDFFZThNQUkiLCJtYWMiOiJlMGJhNmYzOWU4NDMyOTkyYjNjMmU4MWQzNTRlNWNjZTBhZTk2ZGY3YTE2ZTNlMjUxY2I5MjM0YTc0YzJhNzRjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:41:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik8xY052ZTkxVVEyZXZDc1Q3V3R1MWc9PSIsInZhbHVlIjoidUxqOFpvSlhUNThCalpreE1ORWtiUTBTcmxXcFZTUnJkNjlMc1JTeENuSXhoK3JlVldIUE1oMjZBRWJZdStwbkp4SVhwZldIbnl4ZWFKb3FPZkFEUy9pVlNHa0M0d1lWem54akxjaytjZzM2OEVUemp6eFM0eUxCSlk0T1lkdlZwam1UdGM4Tkd2SWk5S3FENGp0ZW9yYkFzRU05OXl2M0RUdWhEZ2dueFpDZUZNYnhYb2p1aENoZVFmeldEQXZMVnpEUGNGOTNQVVAyVGlyRUlUc2dOcTUybXh2NUFnK3RORHdDL2s4ZTkvaVlNaWV0d2tqdXNrc1M0Rkp6SVJvenFBeWE0T0ZKWFBlTVF3WE13UG9qdm9XYmNUOWdaY1I3MEVadWZFNUE4clVSWFpuT20xcmVKZitiRC82NHNwSXIwZ3BZbnh0c1ZqQVZHdE5WclgvRzhnUTBmc0ZmdkxpZXVHc1p5ZzV3ZG9EaFM1QnVUWU5LTFVrT2wyZk1jZzJDVE1WNHJmaEtMYnNDV09yN0FZc1BKVDJaTElTOHkzWjViQVJPTTRXTDVBaGZodURrK2lNZEY3c1JJeEJBWGNCOHRTNjhDZEVTbVdjTGdGMDNmRUVuTm4zU2laUXJhOFIxYWx4VDVmRFBUNVdySjJFMnErelh2L2Z2QXdFZHllanciLCJtYWMiOiIwYWZjMWU3NGY5MDg5MmNmMGM0MDE2OTk0ODliNzk1ODlmYTc1OTMwODg1MjczNjgxYmY2NmI0NjM0Y2E2Yzk0IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:41:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InphcEhUYUkzNit4MXlZYmZ1WUl5QXc9PSIsInZhbHVlIjoiUVpyMkI5Q2JSWWl4Z1hLRkcyMXpHTllpRml1Y1ROckxPcmo3WnRiME9uV2FZSGNLandpb0p6Unk3eW1mcjJjTXhUb01BTCtFb2NNWTVaTloxSXRhM01leFI5WjVHdHhKanJiSm9mV0hBTTh0d2h4Rm9jcDNBMG5MRE1samNqb1pqK085YkI5TWg0cDVwdGd4aDlKakRCSEJYU0VKcFJ6VHZ2S0ZCNXJxWTlMenlmZEIvWi9OU3hkZkZ6ZVVIdzRzbHJocDMxUU9uZjdsbmlzOHhXUEx4c3NZSUkvVml1cGdOK08zUHN2K3VDcTVjQnBUWWpJVW83c1o4Uk8vSURwbjlIKyt2UWxvU1J4L011K3RyaGhHYjI3WFBLK3NFYWg1NW4zTUduaWZXdFJ2NmRmZDIraHZZUXhxMllHRHNNVWEycW53NnIzc2Y4OWRkaHBaTlVWeVZvTGZZZFkxNTRXalpsMmFUY1VCTnlTaklub0hKbUg5c2VWSlphSEJha2pJTjhzZlBEM3RITEZyZ1R0SWwvcTZMb2xUYWtFbmh4eFJhNTBBbjl5eUJrRUFzQmpZSnYwQ2VBSENnY2NHeWlsU0FIUDRmQWNmRlZXV2FPOWdWNHp6WjRFaDVmZkt0Wmh0bGN6Ny9TYjYyVzJOVGx5MFZ1R0pxL2tRaDFFZThNQUkiLCJtYWMiOiJlMGJhNmYzOWU4NDMyOTkyYjNjMmU4MWQzNTRlNWNjZTBhZTk2ZGY3YTE2ZTNlMjUxY2I5MjM0YTc0YzJhNzRjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:41:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1870821\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1331902155 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5bAS6pxlTjQQmoANE7jULrxXjNgNlmc1m9mk4gE8</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://localhost/omx-new-saas/finance/sales</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/omx-new-saas/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1331902155\", {\"maxDepth\":0})</script>\n"}}