{"__meta": {"id": "X2e7c3ebf41d1cafade47d7f66937f4f8", "datetime": "2025-07-30 10:44:05", "utime": **********.243969, "method": "GET", "uri": "/finance/sales/contacts/search?search=pa", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753872244.509333, "end": **********.243995, "duration": 0.7346620559692383, "duration_str": "735ms", "measures": [{"label": "Booting", "start": 1753872244.509333, "relative_start": 0, "end": **********.147104, "relative_end": **********.147104, "duration": 0.6377711296081543, "duration_str": "638ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.14713, "relative_start": 0.6377971172332764, "end": **********.243998, "relative_end": 3.0994415283203125e-06, "duration": 0.09686803817749023, "duration_str": "96.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46721216, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1238\" onclick=\"\">app/Http/Controllers/FinanceController.php:1238-1305</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01235, "accumulated_duration_str": "12.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.198211, "duration": 0.00709, "duration_str": "7.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 57.409}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.219149, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 57.409, "width_percent": 6.64}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%pa%' or `email` like '%pa%' or `contact` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1254}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.224797, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1254", "source": "app/Http/Controllers/FinanceController.php:1254", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1254", "ajax": false, "filename": "FinanceController.php", "line": "1254"}, "connection": "radhe_same", "start_percent": 64.049, "width_percent": 13.846}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%pa%' or `email` like '%pa%' or `phone` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1278}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.231034, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1278", "source": "app/Http/Controllers/FinanceController.php:1278", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1278", "ajax": false, "filename": "FinanceController.php", "line": "1278"}, "connection": "radhe_same", "start_percent": 77.895, "width_percent": 22.105}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1613430333 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1613430333\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1839748218 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">pa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1839748218\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-258466116 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-258466116\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1836738007 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImlXQ2JIdlFBbmZYQnNaekFPZzBhNFE9PSIsInZhbHVlIjoic0ZSR3UyM2tydExER2h3ZUlYbU0yeXlwNmZSTDlHQzI4cGwxTDE3amJ1NUh3SWpTMFdWZitzMTlZNEtFS0loNzJSQVpVcmRSVmJKM2JjMzF5QmdWaEFURFVFNDE1UE1Oby9ReTNEUVN0VVFSbFI0aWJycERLTnQ4RlpZK1gwYnU4cC9BbDRsa2pWbldHUEpkVy9id3JYaUtJanFNeFpkT01NczRNQVR5c1g3QnVyU2JMVjVJUExJY0loVGNpem05MHllK090eW1saW9lVHcrRTVlMVpURkhGN0IzY3JqV2RDb3Y0cVpZMFg5a0VkWWE2VEVvNUFIQU5iQzZhQjVEUllkK3R5c2R5UGFXbkVOd3hlMFp6Ym5ablN3NnE5bmxaOWdhQlUwajYwbnNPM3NrY1BYRWxpM3dzTUE4TnlqejkzbHVENTZFcEJFTTdHUU1mUHFmaFg1bzdvalUxbFYvaXJqdCtFbHJnTmJ2dWM1bGFlQkJXeUJtdTJLYVBUSXJXL01pVHpqMm1pODk3UmZFVXNUQjZlN0p5bzJqdnE2K1hIaEZCQWc5d3ZCcmZYeEJJNG8zNmdBcE8xRmJyQVJ0OStPYmRqM04vZnd1N09qOFQwcG92VVlZVDdpc3V1VTZCbDRYbnFZb1VGK3pseVd2RlVGMUpvMnpoTE1LZURYcVYiLCJtYWMiOiJlYjA5Yzk1NDI0NDZhOTYwOTk5MGY3ZjE0Y2VlNTc5Yjg0ZDBlMWU4MTUxNjMyYjQxYTdiMjdhMzI1YzMyZmY0IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IktZQzVTbmtOb2wzdkNjRzNEUW1wZVE9PSIsInZhbHVlIjoiSkZCcGNEbWQ4UVFzaEtHaWVTbmg3V0RtRFk3TEd4R3QyMEVWc01IaTJXbWtLQmRxMmhXbUx1Y3BHNUZ1b2oxdjg1SnZXUmhjTlR5MWVFemZTQnlTMStUMU5CNzl1S0ZFKzZHZ2UwRGhMaDJYRkxuVmwvZHoyT1dnTDNJTnBsK0hJU25RelJFdjZLT0Z4UE1tOUprSVFFSVdob0ljM09SbHQ0cWNCUVhxUXZnZXppc0VWZWhZTHhBelAyUEg1MmRrTkJ0b2o4VDFuc2EwMkZ5d29RK25pU1ZmMk4xMkgzQlA0M3gxSTRHcnNOSm1zazF5OTBuWTI1d2tvSk5pdExmQllJZ2gzTzNUR3JTMjZFN3R0RVVXK0x5NTNKSVRoVGpHbzFvdVEvNWNTSW1YaS93K2FxeG5sNWNkNGhYdG03RzBMczJ6QVhyMCtTZFB4S0JHVEdSdlZRTWFIaUJlS1phNTB3SnhYaXFFd29IdUg2WUlvZURsNTZ1T0dpTEVSUGx1Y0krTzRERXFUZTlsTWdrbE9GdzhmcndwVnRnck5TZVpHdWlidGtUbVpzekc1N1BhZ21mV0FJTkhWSU1XQjRoVHpYRktsTXk5WjJMTm9idEJBWDBpVUtYaDgwTnB2dkcydXdyUWMrUzVwQk1iWGh4ZWZqNGEwUnA3VjBFQVB3SkIiLCJtYWMiOiJmNjk0NDZkMTk5MGVjN2EwODhmYTY4YjJhYjMzMDBkYTRhMzU1NjQ5ZDZjYTE2NzNhN2RmNTc1MDRjYmI2ZGUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1836738007\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-92061839 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-92061839\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1740389445 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 10:44:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikx3Yi9kMkRGRFNMelpFNVlCSmE5Mmc9PSIsInZhbHVlIjoiZFRFcnZYb1dFQUVtWnUrNE5Xd3ovRjc2SjY4SnZ0VEhheUhoMmRHQjZBeGk2N2NEUlV6YjduT2Rlc3RXYVRQWEN2bUV5bFc2YnVMNzRvOWVIWGJaRWVqc2ZGazRXaUxHSWdUamtIaHdOVDFsbXg1bUN2S1JtN01NWUtXdkdtUkRMNUVBTXJCS2FDSGhweTJoTlB4UnZiaGVOV2d0Uk1yc2NNczdnZ2lpYzNqOGdlT2tJaXBkMDk4QWs2c1d6d25LaEpzamx5ZVBnbEpNMm9PR28zS053Y0U5V04yZnlRYjhtUm5QZmNQSXNjZStzZXl2RWxKekdFQXIwenJPalhsTEpDQ2JEaC9kRHlmbjltcVhZcEVwSUN1S2oyaHliSGxCVjQ3eTVhQnRXbVJrN1BzRGRrdjg4RnZDTzN4QjUyTzVKUis2eldSSlNRK3VkSEtjTDBJS2JZUllyLzZpVmU2WVZlS0RvdlhtWGtsY0Nna2xXMjlZYWJXUWVoK2UrOW15dUg5THNSSXhKUmxJS3ZzVWVhd3F4UncyQjdEWEtFS21hamdmeHdlNGFHMHU2dDRuZVNtcXNEUC9oc092K2tZWWlRZ2czS0t0NXYrZm93UFVtNkYwZU1NWElUMEVlcWozby9IK0FRRUd4VnNsNWJUU0RVamt5bXRsTCtoM3l0NXEiLCJtYWMiOiJiZWNhZjQ4YjFiNmY1NzA2NGJlOGY1ZTAyNWFlZTdhNTY4YzNjY2JjY2U5YWI5MDZjMTFjODk2NDFhOWQwOTRiIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:44:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Imx0R0ozOEIvSjFiOTdtZHRwZ1ZDOVE9PSIsInZhbHVlIjoiRThGWTNGWCtJWlE1UkZ6cHpDVStPVEpMcUdDSFlnc3FlUlNYaXEyR3gyUnlNL1REMzR6VHQ4UzBPV3NoTENvdHYzOXQzWDhzN1Z0V3BYdm1jYTUzWmsycnpVblgraGVhL0RtbEllTi9wZG1PZnVtekoxeU4yZUVDUVdmUFh0OEo4T1o2N0FWcFVnR1lob2tPV0JhclRkYSt3OXhzdTFRc2Vtd1NlZkp4Q3VYQ3c1cFJBRDdHNU9zNTIva2VNVEhhakpLQldnVjlwNkwyNWdzdEJsTjlYanp2d1l4aFErNDdMRC9LQ1o4cEFCdGxCdUxCV1kwK25sVkNrSCsrNGRHRm5xZjRyU3hBekcwWDlkZ3dYK3lubTh0QkdmNFlxem9icVova2JYL2U2QmR6REcrVEF3Q0VLK0tBU290czlTU3lXT05GL3BNTEM5MlN4NHVIWEZsc2xxZ0hpOEhsR2swS0k5NTVVTE9rdFhmbncyRC9sRmVLdjQzdDJWR3hUVlVBZWxuLzFmRUtzS1dPOGxaN2JOTXlXeHVsQ1J5SHZsaHhNRzg0aVVDYlhvQ29lRS8ycjlwbFZNbzZIMXA2Z0VXbk9GSWJEeC9DNGlpQ29UcThUL0RqSGRoNlRQbyt5YXVwNllzRHArcnFIM28vMzgxYXRmRDNHQVNLQlV6cThldnoiLCJtYWMiOiIxZDc4NmE3ZmI0OGEyMjA2MTYzYmYyYWYxZTRjNGRlZGE4OGRmY2NmYTY3ZmFlN2Y2YzlmZGNiOGY0MjQ5NGFjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:44:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikx3Yi9kMkRGRFNMelpFNVlCSmE5Mmc9PSIsInZhbHVlIjoiZFRFcnZYb1dFQUVtWnUrNE5Xd3ovRjc2SjY4SnZ0VEhheUhoMmRHQjZBeGk2N2NEUlV6YjduT2Rlc3RXYVRQWEN2bUV5bFc2YnVMNzRvOWVIWGJaRWVqc2ZGazRXaUxHSWdUamtIaHdOVDFsbXg1bUN2S1JtN01NWUtXdkdtUkRMNUVBTXJCS2FDSGhweTJoTlB4UnZiaGVOV2d0Uk1yc2NNczdnZ2lpYzNqOGdlT2tJaXBkMDk4QWs2c1d6d25LaEpzamx5ZVBnbEpNMm9PR28zS053Y0U5V04yZnlRYjhtUm5QZmNQSXNjZStzZXl2RWxKekdFQXIwenJPalhsTEpDQ2JEaC9kRHlmbjltcVhZcEVwSUN1S2oyaHliSGxCVjQ3eTVhQnRXbVJrN1BzRGRrdjg4RnZDTzN4QjUyTzVKUis2eldSSlNRK3VkSEtjTDBJS2JZUllyLzZpVmU2WVZlS0RvdlhtWGtsY0Nna2xXMjlZYWJXUWVoK2UrOW15dUg5THNSSXhKUmxJS3ZzVWVhd3F4UncyQjdEWEtFS21hamdmeHdlNGFHMHU2dDRuZVNtcXNEUC9oc092K2tZWWlRZ2czS0t0NXYrZm93UFVtNkYwZU1NWElUMEVlcWozby9IK0FRRUd4VnNsNWJUU0RVamt5bXRsTCtoM3l0NXEiLCJtYWMiOiJiZWNhZjQ4YjFiNmY1NzA2NGJlOGY1ZTAyNWFlZTdhNTY4YzNjY2JjY2U5YWI5MDZjMTFjODk2NDFhOWQwOTRiIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:44:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Imx0R0ozOEIvSjFiOTdtZHRwZ1ZDOVE9PSIsInZhbHVlIjoiRThGWTNGWCtJWlE1UkZ6cHpDVStPVEpMcUdDSFlnc3FlUlNYaXEyR3gyUnlNL1REMzR6VHQ4UzBPV3NoTENvdHYzOXQzWDhzN1Z0V3BYdm1jYTUzWmsycnpVblgraGVhL0RtbEllTi9wZG1PZnVtekoxeU4yZUVDUVdmUFh0OEo4T1o2N0FWcFVnR1lob2tPV0JhclRkYSt3OXhzdTFRc2Vtd1NlZkp4Q3VYQ3c1cFJBRDdHNU9zNTIva2VNVEhhakpLQldnVjlwNkwyNWdzdEJsTjlYanp2d1l4aFErNDdMRC9LQ1o4cEFCdGxCdUxCV1kwK25sVkNrSCsrNGRHRm5xZjRyU3hBekcwWDlkZ3dYK3lubTh0QkdmNFlxem9icVova2JYL2U2QmR6REcrVEF3Q0VLK0tBU290czlTU3lXT05GL3BNTEM5MlN4NHVIWEZsc2xxZ0hpOEhsR2swS0k5NTVVTE9rdFhmbncyRC9sRmVLdjQzdDJWR3hUVlVBZWxuLzFmRUtzS1dPOGxaN2JOTXlXeHVsQ1J5SHZsaHhNRzg0aVVDYlhvQ29lRS8ycjlwbFZNbzZIMXA2Z0VXbk9GSWJEeC9DNGlpQ29UcThUL0RqSGRoNlRQbyt5YXVwNllzRHArcnFIM28vMzgxYXRmRDNHQVNLQlV6cThldnoiLCJtYWMiOiIxZDc4NmE3ZmI0OGEyMjA2MTYzYmYyYWYxZTRjNGRlZGE4OGRmY2NmYTY3ZmFlN2Y2YzlmZGNiOGY0MjQ5NGFjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:44:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1740389445\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2127444211 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2127444211\", {\"maxDepth\":0})</script>\n"}}