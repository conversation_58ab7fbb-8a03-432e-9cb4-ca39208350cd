{"__meta": {"id": "X05972e2bd3bb0102654347fb14b714e1", "datetime": "2025-07-30 09:35:49", "utime": **********.844407, "method": "GET", "uri": "/finance/sales/contacts/search?search=pa", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753868148.986479, "end": **********.844432, "duration": 0.8579530715942383, "duration_str": "858ms", "measures": [{"label": "Booting", "start": 1753868148.986479, "relative_start": 0, "end": **********.725938, "relative_end": **********.725938, "duration": 0.7394590377807617, "duration_str": "739ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.725953, "relative_start": 0.7394740581512451, "end": **********.844435, "relative_end": 2.86102294921875e-06, "duration": 0.11848187446594238, "duration_str": "118ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46665256, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=905\" onclick=\"\">app/Http/Controllers/FinanceController.php:905-972</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01514, "accumulated_duration_str": "15.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.790045, "duration": 0.012230000000000001, "duration_str": "12.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 80.779}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.813518, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 80.779, "width_percent": 6.011}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%pa%' or `email` like '%pa%' or `contact` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 921}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.825116, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:921", "source": "app/Http/Controllers/FinanceController.php:921", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=921", "ajax": false, "filename": "FinanceController.php", "line": "921"}, "connection": "radhe_same", "start_percent": 86.79, "width_percent": 6.209}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%pa%' or `email` like '%pa%' or `phone` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 945}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.83089, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:945", "source": "app/Http/Controllers/FinanceController.php:945", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=945", "ajax": false, "filename": "FinanceController.php", "line": "945"}, "connection": "radhe_same", "start_percent": 92.999, "width_percent": 7.001}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1322306934 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1322306934\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-68385173 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">pa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-68385173\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-627122949 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-627122949\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IlBjK2NhSEd4MWpTNlRoWnpxd1JHSUE9PSIsInZhbHVlIjoiMlk2dnUxV1FTd2xVcDkvNjR0RjdVUUw5VW1DY3FWWEk3VXMvRnd1eXdTdE01Q0twREYrZlZEUWlxOU1tYTdLbDV6MTVZVFFWcnMxQ05wK1g0Y1N0YzJ6KzRNcnRhY1Fyc1RhRitlNnFGdlljbzFZUGVHUVF2Y0dZdXBFVXlFRFBQUVZzR3VVbHJkcUdKSnVkWnhXeWdYcy9JUGV2Y05pdlZmZ2krV2FpSXFXbHJENUYxcW1EcnpKMkpadlE1NlNsaHNBMVVOZ2lqNXZMWkRaVjdmUVBpWjFjMU1GNHVEc0NOMysvUHBLR0FmWFRNd3Y5VnFLSi9JYWU1V3dHbjlxK1J4TXZES0JDV1JvLzd0dkhMUHF6bXVKNHZSazdIOUlDWVJjdlo2ZUczaUxoOGU2SytsakNSL1JCRldWR3FtV2xUMitaKzhadFM4cjROODZqa0gvT3pHQVFPS1Q4djBpa3I0OUFUWHJwd3RHRWxUQUN5c0VVTXNkcmlCUlFFRHZmM3NHNWlSM20yRWNvZTlYT3o4QnNBNUpBQjhoQ2owWXd1ZGdYdm93UnE1cnhCN2F0dWhHNmdsR09IUEZhWW1vTU02UkVPQVN4b0RoaEY1NDRwczJKUWlwZmZOeEt2a3BDRFRGYkhuZjJjeXpUYjBoSXpHSDdtRDNBeU9PZlBET0siLCJtYWMiOiI2ODBlNTUyYmI0MWE0OWNmM2YzNzcxZjAxZWEyZjYyZTRjOGQzZTc2NjE2ZDNiYzQxZTAxNGJlZGViNzljZDZlIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ii92dk5DdVFma3g4cHUvTlpTdHlCZnc9PSIsInZhbHVlIjoiQlBXS0tzQ2J1Vm9qMjl3R3hJZVNSWnhpaFlqZFhUTXkyQjFQV3pEalpJUDQrM1gxekxDNE90ejQ2UmxSU3d1b0xNZ0NjWUZpRmhtTkthTnBIVnRlczNYUWN0bjlCSk5LNFAvdFM5S1NNek9NV1A0Y21UZFN2ZG56V1lCekUrRzNWUWhEK3FKMjlnc2IvYzVxZmFlVkxUeDhtdzNhSGxsQ3E2Tm1BMFlRL3BRc1E2b1ppUEtnVDlmNmlpYkFCRWRqNkFiM29ORzVKelBvWDk5eWhLcEpIaUFseUVmOC9zQzRudVQ2MG1QN21Td3IrS1FLQm43YUJoZysxVDd3TGZVUXFKK2dKZVFmaFRUSDRFNWVaWWo4dzNGcmY5Tjkwb3JYcHIvRC8xcXI1dnZHdk5nUitRL0Vva2FIMDkzMW0yZmU4M2tydzNKQUxwVTI5a3FEMDlITGdWU2l0RmZBZXZqZ3lSWFhuZEczTnZVUGErcW5xWnV3WU9NOWhhU2xWZ2Y3RVRFS1RDLzhGZENnN0JUa01NZlBtdmY5WWd3eE5oUUM3bUw3WFZPREFYdmpLc1ljTGVqTzg0TkZ4SXgydFFSNyt1VFhxalZLajhJRk8rOVZ5L1dDSjNqb2ZKMmNYMElyMm1RbUpLektpTG15Ry9oV25ZSXJ0QmRDenRlZnpDNWkiLCJtYWMiOiIwODliOWRiMTU4YmViZTExOWM1MTE4MWQ5MWE2M2RjNzM2MTdhYmJlN2I3OTZmYzJiYTM2NTBkODVlODE5MjRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-747605522 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:35:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRZV1ZCZE1kVzFlbWNQVlFkcjB1THc9PSIsInZhbHVlIjoiUkNVRUpHVWRPTllzTWVDWEV2RWswUFByeUhuTGxUYWw2R1cvVUZiZWpUeGJyMGJoa3Fnck94VWRSVEsxdDY0bTlTSWFGeU5DS1VVekN6YzhLWm1GdjBPR2RIc0lLN3pLYUdoWVRRckZva3ZsOG9lWGl0TzFHLzdVL042MS9WeGpERlNMaHJ0d0RHYWVJcXRnT0FrTmNOUHhqZ1NVL3FoZ0RlYW5HRWY2eEhFZHdJQy9meE9sc1ZBZTU4MVg5UVMyREJHaDlEdUxuWVFwUmpiM2tMN3VqRVRpZXQzT0dYQ0Q2aWFPUU9vQU1uWmhLU2Rnb0JoM3VhbXU5WktMQXE4THJjVHRBcWhmSlRLamVOUk9vVGZ0d3lxQkIraWZITEVkMXJGSlZpWURHaUsxS0hRaXJ3bkNXdkhEUlJsSHQ5NDJsN3J3UFQybHZtdkJWc2NVbzNGbGhDcnBNMjluaEROK3ZIckR6OG00cWJXUWpYTW1peTc2TG1Da2lzWWVBb1RFUjNyOGRzV1FaSEx5L0ZwL25aVVBmQkhZVmh1bTJ2YTd1RDRuWGZMSGxlcEJEcWs3dWVMVW9GeUpJSW5KWHdkOWtnMEpwanc2bEN5ZWRvM2ZaaFU2QjZrWkp1OWpqM3RDcjVKdUljQTgyblZOSUhaa3o0dXZjSGVQTDJrSEIwcGYiLCJtYWMiOiI0NmQ4ZDQ3MGIxNmI5M2RmZmYyOTg1N2QxOWVhN2Y3YmIwYmE0ZTk4YTRmZDUzOTI0OWUzZmY0NDI2Mjg1NTZjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:35:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InNiRWUvRVZCVEtieTNIUkh4aThrb1E9PSIsInZhbHVlIjoiN2V3VlZtTFJuVTFmTWpQalNjLzRYTjZVOTBqWFB4Zk9hNU1Xcm1sL3dSS01JZHdqUURTTWEzVmUrM2dNZ2MvTFJYOTNZVUVUcjVoY1NJeWNKMmF3VkFWLy8xU1N2WjVKWUNpVkNIWmZCdWhmckJkYkdJNWY3VEQra0M3V1pMdGN1SXliYlhmOUpXNE53S1dmdHVzcWJnSzd4QnhyMmRzQVRJM3F4TnBEakdLUERKVjBab0FTZXI1bitwNUZReGoyVmVUcmRhNS90RExhU1RjcWxCVFdWNi9zSG5iU3RKSDJSeWQ2QWMrd0krMTBtbnZOK05tQ0pkMDh2WjZlaVkrZkhkeUw3djVBWDkvQ2FVdTRBQjA1YWlRcHFoaXgvdmo5U0FmZnRlV0N2V0xWR1ZJU1lyRHd1N1A4TjgzUEFhNngzNzVoTDV0MDNQY2VXRytUN0RhaWpvUURBdDJDU1BheXkwbmt2TVZCNWI5bm5nQjl6NncxWkg4b3dNMmZHdTk5cmpnSTgzVGdxZ1R3bWJhekxFVmFhSW9XOEtXWWNrVTVEZmJjZEdkZkxXN1IvcjUwLy9SQXBxM28wVUdnSHBia0pKSFI3VHBGVG0ra2FWTTMxRkZJYkZaMWl5ZU5TMUZwNndtN0dZUUwwVGc0djNPSDFxWW5ISVdZQkJzZFBSbDkiLCJtYWMiOiIwMzRkYTkxMTM1OTk0ZGU5MjJjYTQ4ZmRhOWFkZDMzZDNiYTE0ODI4MWFkZjExZDhmNGI1OGNjOTgzZDczMzdiIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:35:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRZV1ZCZE1kVzFlbWNQVlFkcjB1THc9PSIsInZhbHVlIjoiUkNVRUpHVWRPTllzTWVDWEV2RWswUFByeUhuTGxUYWw2R1cvVUZiZWpUeGJyMGJoa3Fnck94VWRSVEsxdDY0bTlTSWFGeU5DS1VVekN6YzhLWm1GdjBPR2RIc0lLN3pLYUdoWVRRckZva3ZsOG9lWGl0TzFHLzdVL042MS9WeGpERlNMaHJ0d0RHYWVJcXRnT0FrTmNOUHhqZ1NVL3FoZ0RlYW5HRWY2eEhFZHdJQy9meE9sc1ZBZTU4MVg5UVMyREJHaDlEdUxuWVFwUmpiM2tMN3VqRVRpZXQzT0dYQ0Q2aWFPUU9vQU1uWmhLU2Rnb0JoM3VhbXU5WktMQXE4THJjVHRBcWhmSlRLamVOUk9vVGZ0d3lxQkIraWZITEVkMXJGSlZpWURHaUsxS0hRaXJ3bkNXdkhEUlJsSHQ5NDJsN3J3UFQybHZtdkJWc2NVbzNGbGhDcnBNMjluaEROK3ZIckR6OG00cWJXUWpYTW1peTc2TG1Da2lzWWVBb1RFUjNyOGRzV1FaSEx5L0ZwL25aVVBmQkhZVmh1bTJ2YTd1RDRuWGZMSGxlcEJEcWs3dWVMVW9GeUpJSW5KWHdkOWtnMEpwanc2bEN5ZWRvM2ZaaFU2QjZrWkp1OWpqM3RDcjVKdUljQTgyblZOSUhaa3o0dXZjSGVQTDJrSEIwcGYiLCJtYWMiOiI0NmQ4ZDQ3MGIxNmI5M2RmZmYyOTg1N2QxOWVhN2Y3YmIwYmE0ZTk4YTRmZDUzOTI0OWUzZmY0NDI2Mjg1NTZjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:35:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InNiRWUvRVZCVEtieTNIUkh4aThrb1E9PSIsInZhbHVlIjoiN2V3VlZtTFJuVTFmTWpQalNjLzRYTjZVOTBqWFB4Zk9hNU1Xcm1sL3dSS01JZHdqUURTTWEzVmUrM2dNZ2MvTFJYOTNZVUVUcjVoY1NJeWNKMmF3VkFWLy8xU1N2WjVKWUNpVkNIWmZCdWhmckJkYkdJNWY3VEQra0M3V1pMdGN1SXliYlhmOUpXNE53S1dmdHVzcWJnSzd4QnhyMmRzQVRJM3F4TnBEakdLUERKVjBab0FTZXI1bitwNUZReGoyVmVUcmRhNS90RExhU1RjcWxCVFdWNi9zSG5iU3RKSDJSeWQ2QWMrd0krMTBtbnZOK05tQ0pkMDh2WjZlaVkrZkhkeUw3djVBWDkvQ2FVdTRBQjA1YWlRcHFoaXgvdmo5U0FmZnRlV0N2V0xWR1ZJU1lyRHd1N1A4TjgzUEFhNngzNzVoTDV0MDNQY2VXRytUN0RhaWpvUURBdDJDU1BheXkwbmt2TVZCNWI5bm5nQjl6NncxWkg4b3dNMmZHdTk5cmpnSTgzVGdxZ1R3bWJhekxFVmFhSW9XOEtXWWNrVTVEZmJjZEdkZkxXN1IvcjUwLy9SQXBxM28wVUdnSHBia0pKSFI3VHBGVG0ra2FWTTMxRkZJYkZaMWl5ZU5TMUZwNndtN0dZUUwwVGc0djNPSDFxWW5ISVdZQkJzZFBSbDkiLCJtYWMiOiIwMzRkYTkxMTM1OTk0ZGU5MjJjYTQ4ZmRhOWFkZDMzZDNiYTE0ODI4MWFkZjExZDhmNGI1OGNjOTgzZDczMzdiIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:35:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-747605522\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-424496359 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-424496359\", {\"maxDepth\":0})</script>\n"}}