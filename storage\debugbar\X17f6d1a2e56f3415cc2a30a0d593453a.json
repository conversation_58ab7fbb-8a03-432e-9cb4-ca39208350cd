{"__meta": {"id": "X17f6d1a2e56f3415cc2a30a0d593453a", "datetime": "2025-07-30 10:17:57", "utime": **********.128803, "method": "GET", "uri": "/omx-new-saas/login", "ip": "::1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753870675.719927, "end": **********.128832, "duration": 1.408905029296875, "duration_str": "1.41s", "measures": [{"label": "Booting", "start": 1753870675.719927, "relative_start": 0, "end": **********.904723, "relative_end": **********.904723, "duration": 1.18479585647583, "duration_str": "1.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.904743, "relative_start": 1.1848158836364746, "end": **********.128835, "relative_end": 2.86102294921875e-06, "duration": 0.2240920066833496, "duration_str": "224ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46666984, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.04407, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.060908, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.110784, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=263\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:263-278</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.0261, "accumulated_duration_str": "26.1ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 545}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 267}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.977711, "duration": 0.01041, "duration_str": "10.41ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 39.885}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'radhe_same' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 527}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 270}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.993371, "duration": 0.00783, "duration_str": "7.83ms", "memory": 0, "memory_str": null, "filename": "Utility.php:527", "source": "app/Models/Utility.php:527", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=527", "ajax": false, "filename": "Utility.php", "line": "527"}, "connection": "radhe_same", "start_percent": 39.885, "width_percent": 30}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 533}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 270}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.009555, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "Utility.php:533", "source": "app/Models/Utility.php:533", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=533", "ajax": false, "filename": "Utility.php", "line": "533"}, "connection": "radhe_same", "start_percent": 69.885, "width_percent": 7.088}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.045379, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 76.973, "width_percent": 3.448}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.062279, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 80.421, "width_percent": 3.525}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3940}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3988}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.091558, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3940", "source": "app/Models/Utility.php:3940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3940", "ajax": false, "filename": "Utility.php", "line": "3940"}, "connection": "radhe_same", "start_percent": 83.946, "width_percent": 8.851}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3943}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3988}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 9}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.100659, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3943", "source": "app/Models/Utility.php:3943", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3943", "ajax": false, "filename": "Utility.php", "line": "3943"}, "connection": "radhe_same", "start_percent": 92.797, "width_percent": 4.138}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.106719, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 96.935, "width_percent": 3.065}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "5bAS6pxlTjQQmoANE7jULrxXjNgNlmc1m9mk4gE8", "url": "array:1 [\n  \"intended\" => \"http://localhost/omx-new-saas/finance/sales\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/omx-new-saas/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-445007933 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-445007933\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2017564190 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2017564190\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1152775551 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1152775551\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImpGRVozOXBjTDhDZWNJNGxDV0RPVnc9PSIsInZhbHVlIjoiakkzTFdYUW42OGc4SEFpTk5uMmNvSWlud3pRMnFiMHVlVjlTQ3JHL0owazBsZVdpK3RkVU9BYUtGZ0hjWDJQQ0w0enJ5a2Rkd2NQZCtqK0o2TElYLzFESmJmZjNieU1JbkpTNng2SmU2VHI0UXErNVpGMDF1bDc5MGRKK25PY3VuekRka2tzVmtlamFmU0VIUmtsZXBVcUpvc09JaytBUlRBTnZGNzBUQmRlb0x6WDBsZkpscDhkVitSd2g1U2RWUFFIcS9lL21CdnZRNGU1UUVERUUrUk1CV1FRNUJlTFc4MzV0RWtsS1Q3Wnc3cXpEQW5vUi9lc3Y2QTE2bFBMZjRmaTVrY1lSMjZMZTMrcWthZXJOTU5qbXY1SUMvbkNnMnJ3UEJidGZpY2ZrVmFMZFBSV1Bma1RDMDZXMXFMT2Nsalp2VzkyaHZNOWdRdHVTcHkzblduVkhCL2RadExJd3BJQU0wZ2IxNi9ZcDRHZDBkN1ZQNlVTYWVndlo0ZDNZU3oyNDBSWWV4WjJVaG9XYlVwQ0xKU3FBWU5hNllaZUJDYXdPRDZxS0pTeUVaeE1jc1VsbnltMnJWc0I1VEgyajFxLy92bDVvRkpkdzNiV3ZCWFk5TGlWQXFqUXVoV0hBWkQ1N2JTNWtKQXdNSzdiMTlVM0JQMkkwKzEvbG1PczIiLCJtYWMiOiJkMjRmNzhhYWRmN2YzMmY5YWMyYjMwMzhmYTY4NzZkMzM3YWM2MTA2NzA1N2UxYzBmZjJmYTcyMGQ3NTVlNWQxIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjJXMnFUUEJCcnBqYTVrdjBxN1dzU1E9PSIsInZhbHVlIjoiMmpUVk5HRUxHZHBIYmQyQjlYdFFYVUR2RGtzdzAvRi90azFUU28yM3d4bWRGa25NV3E1cDZZaEN2dnpuZ3FXYzNoSmVZLzBUOTIwVnZIcnRrUzRmRGtmWnBmVjV6VDVKN3htV2V4dXU4dFVicmtWRGs5eUlHendObEx3Q1FRWUdQMGpNNU9qaWpMVXpqTENLZ3F3L1VpN1FvTC9xZGFLeUlGQWpUM0cxZ0RWOVQ2QmtJakRuV25LMEd0OHdqNnd5bWo3TmdvdGpnSmt5SENzVUZ6WGR3WEIrc1VlOE5WSnJub1ZCdzNZY3BHU1lLTUFlWmNDbjEzMm9NdGxOZTNIbUtmTzNvSWhzbSt2cHZuZU5mQTlZMUlHcUxQZjJRLzlFNjI0bFNCTjNlT1RvNDlzZXZwaHpsNUN1a0ZKNjJhQkpxZ1lWMHhReTM1M0tYaUdoaTFUWUFaMkVaUXFjbHE1MzN0RVJOSjZoUkJ3RnorWWxGTUxMTmZQNWFzSytqWmVwZG5qTFNpWWk0VTRUNk1uTEc2cHZySUVjZjZ1TzJUMjcyNE5HclpYNXdNYU1UWTlLWkxtTnRwT0NLVEtNTW02UnY5UHhiMFlGVk1ESGpKbGNXWFFRM2krL3lKZjdTTnVDazloOFRacW10aHo1MXJqcHV4N244ZmFyWVVLSG9hZkEiLCJtYWMiOiJiMTg4MWJlYmMzZDdmZWVkYWZlOTAwZTNhZDVlOTgzZDI1NjE1MWE5NDZhOGNhZTBiNGFmYzQzOTAyM2EzNDVhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1019082490 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5bAS6pxlTjQQmoANE7jULrxXjNgNlmc1m9mk4gE8</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7f4EUZP93e4zKShJdjMEQKdIg0VLTOjvnrPmQ2oa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1019082490\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 10:17:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkwvNmhWK2ZNSlphbVl5OW9mZGRUQnc9PSIsInZhbHVlIjoiVFVYUm9Gb1d1cXFpYVoyRlIwZ0dTdElsK2dvUHQ2WGtDY1hHdXl0blNZblNtaE5HYjJaeWxzVFhwdG1Tb1lLWG9ZaUEwcmNEUnB3SGh3a0UrR1ZQVVRqQU9EVTM5em5OOVN2SzdxWDM2WG5nVVU4azdjY2d5TUdTZTRrRnJiVEY3ODI0UmhuVDdzU2NLeGtiODZRbW45VmNpaHdIU21uVGJ3R01iV05OYjdUWXByMEJOTTdhRGVRdHJYMDdDWld3ZnlybVZYSHNxSnBIYVA0MDJuNWt4Y24xVldZc0pNV1lrNmk3UmtYQjN3dEtqcS9MdFB5SytoNkZEOU9xc1RBbmJJQWJEd0hBYWhDNmJ6a0ZneStjSFEvcUd6WmN2L2l2dC9Ydi9GbVF3UXBjbGhtdkpka1lsTW14bzhMdUZ1UGpBc0Y4a3owUDM2Vmhxcm5IUC9WSnVSZFcvQUNwQjdFcGNjU0tXdlYybVhIZmF2NnhWVFlWVFA5SUU5SEZYNENPbTBzRXhNYjV5ZEFkL3VDcVFjQitEYktCaHFONnFSMENvb2tHa1o2S3V4ZXN3M2Z3Q0ZRd2pGSmxmZTFjUmR4R0k4M0gyNUVrMjQyNGNVaGdISkJEbHczMjZ4ZXZQREVyMnhGRm9McFNBZ1dMM09mZEMwM0txYVRlVUxKekVaNGciLCJtYWMiOiJhMjE2MjA4NWNkZTJlZTNhYzQyMGVjM2Y4OWNhZjc5MmE0MTBmM2E0ZmYyZWFkNzdlMjIyZWUwMGY0YjcxMTJjIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:17:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InBzRUQ2Zmt1b05uSU00Ym04eXVKQXc9PSIsInZhbHVlIjoiR1NVVGRhZ1lJSEZ0cmhHcG1aVFhrQlNUTzJGWGRTMGdGWVljQTg0RUxTd0lyU2ljTUovWHowV1NvaE9CdHFaRHVOMGF2dmZqMkZrVkpIUmhDdTJKTDVlTnVYYTVsVWVla0toWGpVRVlWczFIbVJIU3lFZHZydUF0K2NrZ01KYmxrZGhqNXRNUjRORUNQUWxQeENXOUQ0WmlNckFsQ1RaY0lBbFZkSEdvWXJYZ0xiclZBWW1pdThPRFE1ajhIT1laTjVuWGdKM0xxQWNOZERlSk5sdlphcGFPUVFBVmUxTnRtZVJOMGF1M1FPN2dSNWt2WkR2cmYxOStTS1RibXJleHRPOTY5SSt3K3czNE9mQ1d6bjhLeEo0dlZ4cEJ2UmZqZTN3L1lhQUUxSFVqZExvbnZrRmErTitBKzJ1d3pyWkRyTUdsYUlQdUhENnVqM042cWoxZGdWZUVGay9ZRzlTNXgyVkl4MUU1QkJLZjdmREJTaGhiMjA1SWhIZVFEYXNjaVRMcURIRFpsVURFWXFVQVcvVE8vZ2ljaHBiUnRNcmFvN2pCM1ZPTmcxNjFKdEhZdzZNa3VWdStmRHN4Q3dPdzR1UTFROUVjVnhiMDVDV0huM0l0L2FnUGhIclVReXJjeHBQaXBXamIvWWVLR1J1aExJMW1VR0pJc1ZLTUpwZzIiLCJtYWMiOiI1MTY1N2NhNmJjYjZlMDdkZTVkMTJlYTgwYTI5MDNkYzgzZWRhZGQ0MmRlMjgyYmVjOTA0OTU4ZTQ0MjAzY2YwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:17:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkwvNmhWK2ZNSlphbVl5OW9mZGRUQnc9PSIsInZhbHVlIjoiVFVYUm9Gb1d1cXFpYVoyRlIwZ0dTdElsK2dvUHQ2WGtDY1hHdXl0blNZblNtaE5HYjJaeWxzVFhwdG1Tb1lLWG9ZaUEwcmNEUnB3SGh3a0UrR1ZQVVRqQU9EVTM5em5OOVN2SzdxWDM2WG5nVVU4azdjY2d5TUdTZTRrRnJiVEY3ODI0UmhuVDdzU2NLeGtiODZRbW45VmNpaHdIU21uVGJ3R01iV05OYjdUWXByMEJOTTdhRGVRdHJYMDdDWld3ZnlybVZYSHNxSnBIYVA0MDJuNWt4Y24xVldZc0pNV1lrNmk3UmtYQjN3dEtqcS9MdFB5SytoNkZEOU9xc1RBbmJJQWJEd0hBYWhDNmJ6a0ZneStjSFEvcUd6WmN2L2l2dC9Ydi9GbVF3UXBjbGhtdkpka1lsTW14bzhMdUZ1UGpBc0Y4a3owUDM2Vmhxcm5IUC9WSnVSZFcvQUNwQjdFcGNjU0tXdlYybVhIZmF2NnhWVFlWVFA5SUU5SEZYNENPbTBzRXhNYjV5ZEFkL3VDcVFjQitEYktCaHFONnFSMENvb2tHa1o2S3V4ZXN3M2Z3Q0ZRd2pGSmxmZTFjUmR4R0k4M0gyNUVrMjQyNGNVaGdISkJEbHczMjZ4ZXZQREVyMnhGRm9McFNBZ1dMM09mZEMwM0txYVRlVUxKekVaNGciLCJtYWMiOiJhMjE2MjA4NWNkZTJlZTNhYzQyMGVjM2Y4OWNhZjc5MmE0MTBmM2E0ZmYyZWFkNzdlMjIyZWUwMGY0YjcxMTJjIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:17:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InBzRUQ2Zmt1b05uSU00Ym04eXVKQXc9PSIsInZhbHVlIjoiR1NVVGRhZ1lJSEZ0cmhHcG1aVFhrQlNUTzJGWGRTMGdGWVljQTg0RUxTd0lyU2ljTUovWHowV1NvaE9CdHFaRHVOMGF2dmZqMkZrVkpIUmhDdTJKTDVlTnVYYTVsVWVla0toWGpVRVlWczFIbVJIU3lFZHZydUF0K2NrZ01KYmxrZGhqNXRNUjRORUNQUWxQeENXOUQ0WmlNckFsQ1RaY0lBbFZkSEdvWXJYZ0xiclZBWW1pdThPRFE1ajhIT1laTjVuWGdKM0xxQWNOZERlSk5sdlphcGFPUVFBVmUxTnRtZVJOMGF1M1FPN2dSNWt2WkR2cmYxOStTS1RibXJleHRPOTY5SSt3K3czNE9mQ1d6bjhLeEo0dlZ4cEJ2UmZqZTN3L1lhQUUxSFVqZExvbnZrRmErTitBKzJ1d3pyWkRyTUdsYUlQdUhENnVqM042cWoxZGdWZUVGay9ZRzlTNXgyVkl4MUU1QkJLZjdmREJTaGhiMjA1SWhIZVFEYXNjaVRMcURIRFpsVURFWXFVQVcvVE8vZ2ljaHBiUnRNcmFvN2pCM1ZPTmcxNjFKdEhZdzZNa3VWdStmRHN4Q3dPdzR1UTFROUVjVnhiMDVDV0huM0l0L2FnUGhIclVReXJjeHBQaXBXamIvWWVLR1J1aExJMW1VR0pJc1ZLTUpwZzIiLCJtYWMiOiI1MTY1N2NhNmJjYjZlMDdkZTVkMTJlYTgwYTI5MDNkYzgzZWRhZGQ0MmRlMjgyYmVjOTA0OTU4ZTQ0MjAzY2YwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:17:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2093154996 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5bAS6pxlTjQQmoANE7jULrxXjNgNlmc1m9mk4gE8</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://localhost/omx-new-saas/finance/sales</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/omx-new-saas/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2093154996\", {\"maxDepth\":0})</script>\n"}}