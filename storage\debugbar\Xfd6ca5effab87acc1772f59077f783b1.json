{"__meta": {"id": "Xfd6ca5effab87acc1772f59077f783b1", "datetime": "2025-07-30 10:18:11", "utime": **********.868742, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753870690.988607, "end": **********.868768, "duration": 0.8801610469818115, "duration_str": "880ms", "measures": [{"label": "Booting", "start": 1753870690.988607, "relative_start": 0, "end": **********.787577, "relative_end": **********.787577, "duration": 0.7989699840545654, "duration_str": "799ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.787597, "relative_start": 0.**************, "end": **********.868771, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "81.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3038\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "6WnSNWj62rWb2L8MC3t9i3i8FDyeveC5yIAHc3GK", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-705735860 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-705735860\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1808400390 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1808400390\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-245468034 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-245468034\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-460936174 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-460936174\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1010331194 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1010331194\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2025693789 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 10:18:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlovczBpZE82VmZsK1M2bmgxWjM2Rnc9PSIsInZhbHVlIjoiOXZKeTRyZUFRU3R1Z2lQdmVMdW5PZTZ1QlczN0lBZkE0R2tvOExYaUozMi9mN0lpQmc3b0owc3pZT3RQcHdLbzlIbVdpMmlaaUkySUt3NHdtbG5yeHJDalNhSVlmckxNemovTkdyaWJ1ZHNiRlpoaTFlMzg5N3lHMzRvZzZLdE5Td2ZLemRSRWRvSCtxRHd0b0N2N1hFUnluZ05uZkdxUXk0aWxTSTQrb1hhQ2IxY0xaRXgyMGJ6Nkc0UUF1RHh4dm52cEQrb0lqSkI2MDRnWm1VTzJiTnEwdzRSVEpFcHV3eHpPcGwvSHhQazd1VEUrYkROQzM3SWFSUnFGRHdTL0FaL2VWVXFHOWhsYTBJM1NqREFZOWxGcmoyWExkVnlJeEwvZWVWUnJJV2wyMGxRc3lUeG50K3dmZC9VcC8zTTNWcGVJREs1ZHhVVGVmYkIxL2tkVERYUlhNTkxsZlBpRVVaNWpVK0ZvQksydC9mam1FMW9LTWYwMDZKOVh3UlhWVUJPZklMK2hoNVhGbFgrYUdUam9QRUxRY1VoOE44V2Z6cEdnV1hqYmJVR1M0UDZiMzhDZzFtdkZYRW5mMFBza2hZb0lFbHBEY2JYNmtZWU56NXp3TEFhNnZBeXV4N0xTSHhWU3pjRkgwRmxQMzd0cFR2V1ljZy9vekZYelYzNTkiLCJtYWMiOiIzYTkwZDQxOTRkYjk3ZWZjMmE3MzdlZDg5YTA2ODY1YjYyYTE1Zjc5OWNhMmU1ZmM5ZWI0NzJkNDIzNGUxMDgwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:18:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im1leWw1ekVuSlBobUNMU0xkSjdIeEE9PSIsInZhbHVlIjoieVcxcm1xZklRNEt6S1pVa3hTOElsUHRXTm5scnNOdnpQQjB3S2JMWjRSNHh6c0FIQ2dIM09TTXRlRnhkMHljcG83UW9nekQ1aWZUMGVVVGI5dm5wdE1mYUltd3IxdFVBckpIU2FSUFNoV21FSFRXSDVibGdMVGRDNUFIdkk3M1NUSnpBd3ZmdnJBL3Z6OE1sVXZDd0syT0xJeHN0M1hMeXk3bDVQQS9MZUs2ZUxWYm9TWkV4clBEQWlMaDdQSmpyanZ5Z1pSNGVFMHhXSzJvWE05MWdIUTVsa3RsV3Z0QzZDMnFJdnZTbXY1NC9FcVUxVE1LQlRtd1NtMG1IQ25NS2F0RDJOc2NHN1lIckkzRGtCZjQ3UWZOVjZYazBhbUNJZWZTakJ3SDdGaHFGUTdZb1V2TndPa21TMittcDFWYUI2YTZPdWJGRG9peStiV0pXbFpkalpOVnNRQ3lqeWJwUVJKK3krYXRMOTJ0OXNXRWxFSytjbkFMeVE5NGhtZ09oQUViWkdCV1RVTDJvdGE5UWpuYVcwZ3U4bXFFTVc0SlpobnNDN0xJUkQwc3NEeTVMbXFIbnZIeDdhN3dkcFhDVXlVZTZGOFBicFdpeGpTbFNIYkNiNTl5dGpYb1Q3QkRJOG8zeE1SNjFzV21UNFZUcnIyTzJtYjlveEl6QytFdXMiLCJtYWMiOiI0YTU0NTM2YWZhYmI1MzNkZjRlMzg5NTRjOTU1ZWFlNjhmOTY5YzNkYTY3Yzc4MGFhZWVmMDdlOWI3MmY3MjgwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:18:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlovczBpZE82VmZsK1M2bmgxWjM2Rnc9PSIsInZhbHVlIjoiOXZKeTRyZUFRU3R1Z2lQdmVMdW5PZTZ1QlczN0lBZkE0R2tvOExYaUozMi9mN0lpQmc3b0owc3pZT3RQcHdLbzlIbVdpMmlaaUkySUt3NHdtbG5yeHJDalNhSVlmckxNemovTkdyaWJ1ZHNiRlpoaTFlMzg5N3lHMzRvZzZLdE5Td2ZLemRSRWRvSCtxRHd0b0N2N1hFUnluZ05uZkdxUXk0aWxTSTQrb1hhQ2IxY0xaRXgyMGJ6Nkc0UUF1RHh4dm52cEQrb0lqSkI2MDRnWm1VTzJiTnEwdzRSVEpFcHV3eHpPcGwvSHhQazd1VEUrYkROQzM3SWFSUnFGRHdTL0FaL2VWVXFHOWhsYTBJM1NqREFZOWxGcmoyWExkVnlJeEwvZWVWUnJJV2wyMGxRc3lUeG50K3dmZC9VcC8zTTNWcGVJREs1ZHhVVGVmYkIxL2tkVERYUlhNTkxsZlBpRVVaNWpVK0ZvQksydC9mam1FMW9LTWYwMDZKOVh3UlhWVUJPZklMK2hoNVhGbFgrYUdUam9QRUxRY1VoOE44V2Z6cEdnV1hqYmJVR1M0UDZiMzhDZzFtdkZYRW5mMFBza2hZb0lFbHBEY2JYNmtZWU56NXp3TEFhNnZBeXV4N0xTSHhWU3pjRkgwRmxQMzd0cFR2V1ljZy9vekZYelYzNTkiLCJtYWMiOiIzYTkwZDQxOTRkYjk3ZWZjMmE3MzdlZDg5YTA2ODY1YjYyYTE1Zjc5OWNhMmU1ZmM5ZWI0NzJkNDIzNGUxMDgwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:18:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im1leWw1ekVuSlBobUNMU0xkSjdIeEE9PSIsInZhbHVlIjoieVcxcm1xZklRNEt6S1pVa3hTOElsUHRXTm5scnNOdnpQQjB3S2JMWjRSNHh6c0FIQ2dIM09TTXRlRnhkMHljcG83UW9nekQ1aWZUMGVVVGI5dm5wdE1mYUltd3IxdFVBckpIU2FSUFNoV21FSFRXSDVibGdMVGRDNUFIdkk3M1NUSnpBd3ZmdnJBL3Z6OE1sVXZDd0syT0xJeHN0M1hMeXk3bDVQQS9MZUs2ZUxWYm9TWkV4clBEQWlMaDdQSmpyanZ5Z1pSNGVFMHhXSzJvWE05MWdIUTVsa3RsV3Z0QzZDMnFJdnZTbXY1NC9FcVUxVE1LQlRtd1NtMG1IQ25NS2F0RDJOc2NHN1lIckkzRGtCZjQ3UWZOVjZYazBhbUNJZWZTakJ3SDdGaHFGUTdZb1V2TndPa21TMittcDFWYUI2YTZPdWJGRG9peStiV0pXbFpkalpOVnNRQ3lqeWJwUVJKK3krYXRMOTJ0OXNXRWxFSytjbkFMeVE5NGhtZ09oQUViWkdCV1RVTDJvdGE5UWpuYVcwZ3U4bXFFTVc0SlpobnNDN0xJUkQwc3NEeTVMbXFIbnZIeDdhN3dkcFhDVXlVZTZGOFBicFdpeGpTbFNIYkNiNTl5dGpYb1Q3QkRJOG8zeE1SNjFzV21UNFZUcnIyTzJtYjlveEl6QytFdXMiLCJtYWMiOiI0YTU0NTM2YWZhYmI1MzNkZjRlMzg5NTRjOTU1ZWFlNjhmOTY5YzNkYTY3Yzc4MGFhZWVmMDdlOWI3MmY3MjgwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:18:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2025693789\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-53658668 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6WnSNWj62rWb2L8MC3t9i3i8FDyeveC5yIAHc3GK</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-53658668\", {\"maxDepth\":0})</script>\n"}}