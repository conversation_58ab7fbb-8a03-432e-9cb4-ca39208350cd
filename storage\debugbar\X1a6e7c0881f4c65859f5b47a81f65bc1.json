{"__meta": {"id": "X1a6e7c0881f4c65859f5b47a81f65bc1", "datetime": "2025-07-30 10:19:13", "utime": **********.288737, "method": "GET", "uri": "/finance/sales/contacts/lead/12", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753870752.247947, "end": **********.288763, "duration": 1.040816068649292, "duration_str": "1.04s", "measures": [{"label": "Booting", "start": 1753870752.247947, "relative_start": 0, "end": **********.121168, "relative_end": **********.121168, "duration": 0.8732209205627441, "duration_str": "873ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.121191, "relative_start": 0.873244047164917, "end": **********.288765, "relative_end": 1.9073486328125e-06, "duration": 0.1675739288330078, "duration_str": "168ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46721760, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1310\" onclick=\"\">app/Http/Controllers/FinanceController.php:1310-1369</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.021339999999999998, "accumulated_duration_str": "21.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.219195, "duration": 0.01852, "duration_str": "18.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 86.785}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2649438, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 86.785, "width_percent": 8.529}, {"sql": "select * from `leads` where `id` = '12' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["12", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1336}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.271853, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1336", "source": "app/Http/Controllers/FinanceController.php:1336", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1336", "ajax": false, "filename": "FinanceController.php", "line": "1336"}, "connection": "radhe_same", "start_percent": 95.314, "width_percent": 4.686}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/12", "status_code": "<pre class=sf-dump id=sf-dump-1932446798 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1932446798\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-489860101 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-489860101\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-265087340 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-265087340\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-721044453 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6InE3SGdLbTNUNnpIWWRLRmZHNE1GRGc9PSIsInZhbHVlIjoieU1vMWZYalJhUHJUZVVVT3Fhb0d2b3B6QkRIemtNQzZ2QUozVVBsWTFpMzJkaDlJZE43d3IzcnVTTDk2UjFBcGdFVGx5aDZzTS9lVE5tbUk1V210aDFveUhuZ2hlRkZ6VFcydTExTTlWMTl3QU85WXVOY1FLZFJmeFN4K2Q1UlFHSWNHUGdFWlVhc3UycHorRlV0VXY3THcrRitQcGtqbHo0NnZGS3hTQUtzMEhWaTZJTnFGb0dkbXYwRUg4ZlJCVk1DZW9sK2VRY3JrbGRaLy9zQzBPbDJUUWRyUFhkZVVTYWViZVVkaDZic2lWMG5BK2J5ZXVmaTBJcTJtcHArVUU4dG9oRjlSbnlaZXBDRTFxZnpxNk1xMGlzcHBwcjM4emdsQ01NRS9jTTI1OE1vMVd2bXdJRU9DWnJ3Y25rYmlTNXVaTkxMbkMzM1F3dXpEVmFGZEJaVnhjc0VUUU5HTVZhc2xLeEFFa3NTelpxWUdsUHgzaDZtZDdIMlgzdTN6dG5yKzg1bjhjUE5GdmxKUTJJQ0MwblIwN2pYNm12aWhUQThTQi9QVFJsU1l4UzRjbTVxWmNCMG5uTkM1VldISUlNL3V2TkYzbndrNEdSd2RqSEFtUG5zM0laRERDK3NzaExwNGhXSTY5cXhRQ2ZoOWcwbDU3WFlsQU9tR213ZDUiLCJtYWMiOiIzNmFhYTViMjNmZDZjNDMwMzllYzRmNzQ1Yzg0ZThiNzFhZDgyOThkMDVmNjBhM2I2ODRlMDgwYjI1NzRlMTUxIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InNYSFFNalNKUFZEQTRsdzAyZVZQdlE9PSIsInZhbHVlIjoibm5DeGdBOEIwYm1EN2NicDVyaEpDR1hzT0daY2hkczZUQUFiQjlmQnNjSE52MVc3TllkNEJkMzFQRzNJY2dQckg5RldmMDlFZ2EzdkdHNkgwQUVFOGwrR1NGNmN3UTZSVkNFSjFFQXFJRU1MNEszd3czVzdxVDJlZUZsYjRDeFlRT0lmc254aXk5cFdHbFRUVUNLQnprYjdmSEMvZjd0VDNVUVYvMVNRTXlFZXgzRzhvWHR3alpQMkp6ZHF6ek9tM2s2UkFKeFc1ak04Uk8zMTF5NmsxZEJjQ3J4MitELytQOUpsQ1M5N05TU2tYRGhCWkRiNjFCM0Q3SnVNYlNVa2p1K2xEeUNhZSt3QlM0VHRLdFRrZGd6cjRvNDdYTWtjTmx1UVdkL0FEYUZPR0xZTWJ3c3VpZWNkQ2JZVHZPcG94VjVTanRJZVJsNmNoL0d5VFV1bDFiNzlLRncybThEdnVrRzNHRDBVUFNXdmV4T0l4bG90eWlrTFRLQVRDRUp2OXRYWFZXckxHeGVzZzFaRHNrcENPVzVIMzcxQloxRHFocVk5dW1pZWpwM0xQbzFBM3A5dnJQWVArYkcyblQ3eDZWY2FQSUxKVXd0RDBnU3hOYmJoS1RGNE9sUEtTMG1vdkRuT1ZKUng5SE50TUtJWFMwZmY5RC9MZTJ5SnkwZWwiLCJtYWMiOiIxNzY1M2I1MjA2Mzg0ZTFlMjk4ZTA1NjFkMTUzNTQxZTg2NWVkZGNlODAzNTE2YTljZmUwZWY4NGQ0MmJlYjlmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-721044453\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1068527070 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1068527070\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1804502742 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 10:19:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFyWTdCSVRxNnR3Z2ZqRjJzdUFhN1E9PSIsInZhbHVlIjoiYXhoK3A1V2xkTk5PNnAzUy9kOExiZzJhbFFTcHp5My81eTZ6UUI5TzQzWVBUazY2OUZFcmpSN1R1ZnQ4QUQyQ2M1bkFNemRlR1BZbU1NRnBBL2s1bVljOU1rOXcyMTlpUDNYUnNQVDRhZXUrQlI5dVlPU1F2Wk9oaytGL0ErbnpsN1NvSE9jTUdueXcycWt2cDFWWTZ0VlN5N1YwajYxamtGU1RBdzZXdWhlQllwWVliTnZBNmV1V3dvYXRLTGVmOE1JSnhGcWNEeFhWanovbVdsT1lTdEVGSWZKWEVvdHQ2RDNCUC9RTWxKdC83Nlhwdm91RzR1SDFQNGNhaVRneHBJMEFDWExTSlJsVWtqeUExcGFjUDBaOEU0empFa2hvd2x3RWV2QnZzMnFUdGN4Z0ZNZS9NaFU4NnFFT2sxN216eXE5TStRK2Vpbmw2NGZmam8yUmtBUUhvRlBoS0x1VlhLdlpxemU3K2NVK0ZjUEM5MU5GTUtHcHcwR0ZIeWRIb09GQnBWTUE5eEtSZXJQYS9SOGM0VEJLckw0NGpxbm5LRDJMVkhTR2FGdmN2RnJqYmNkakVkMHNxK25qQ0ZjSUdwb3pHNzJpb3FCVEZjcFJTVWdqSUFBb3owWlJzYzk1NnpyTXpqTmk5NENlNE1KVDYwOWV2eERlRUpCb0FlZFIiLCJtYWMiOiI5OWNjMDA1MjYzOWI5NGE2OTk3Y2FhYTJmNTYxOWY3NmNjODcwNzZjYjRmYWI3Y2Q2NzlkYzNhOTQ4ODZlNmI5IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:19:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlJYdEFiQ2RtL29nWkF2UC9vWlhnQkE9PSIsInZhbHVlIjoiSC93eStyQ3hJUDFvbHozQjloRTcvcUVkeHdLUjc1UUxUZTNCWWZCdkIrVXFzdkJrSWVjRW5MdW5wOTBDazdMSk5Dbmh2eW9IeFFITG1mdklKbE5uTXFSL0RpbUpTc1grcG9BblZWTlVnRWE3M3dXMklPWXV4b3FLZHhzSm5YV093RjBOMHNYWUFPeHZBN1Z6RWRYUXpmeWdyWGJHSmVUZnlyRDl4RjhYYk5iSmZHWllENEtId3JNbU5YWkVQYWhEK2dGNEJKVG10NnFjejdWd3g2bmJpRGRVT29vcnRqYk5TRGJkL1RhREdzY0VyYjFkSXNxMEovbEl0MUdvSnBpaHFqZ0cxaEgyRTA0Y2VYaHFNNjdtYkhoSGtGSnNYbjJISm1pVndhR05vZCtSVFB5SXU5bXF0Y01HdTR1RDZFSE9BakpXaTFnMi9xNWV1V08xK3lrS1FyL2haenlueC81L3BuSFA1cGRMWERwdVlGcG9wSG1TT0YyeHA4dzdpNWhPSVVpLy9PSFBOOENEb1g5ZitCb0R5cFZXQWtBZDV2MHJ2MDFOcDhITUJNZmdNL2VqNVM3L3dMQkc0V1ZIS3BmeGpWaG9hY2FHTDFhMXZFSHVJRlo4bC9OZzQrclNUY285WGRGMGd6V2dkVXZ3bEpTZEVWejVyU3Y5bnNEU0REcHUiLCJtYWMiOiJmZGMyOTAyMzc2M2FkZTNhYTFlNmE5NzM4NWUyYzllMTFjOGM5OTNjMDEzZjk4NjIwZWJhNTk2YWE0NTUyMTdkIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:19:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFyWTdCSVRxNnR3Z2ZqRjJzdUFhN1E9PSIsInZhbHVlIjoiYXhoK3A1V2xkTk5PNnAzUy9kOExiZzJhbFFTcHp5My81eTZ6UUI5TzQzWVBUazY2OUZFcmpSN1R1ZnQ4QUQyQ2M1bkFNemRlR1BZbU1NRnBBL2s1bVljOU1rOXcyMTlpUDNYUnNQVDRhZXUrQlI5dVlPU1F2Wk9oaytGL0ErbnpsN1NvSE9jTUdueXcycWt2cDFWWTZ0VlN5N1YwajYxamtGU1RBdzZXdWhlQllwWVliTnZBNmV1V3dvYXRLTGVmOE1JSnhGcWNEeFhWanovbVdsT1lTdEVGSWZKWEVvdHQ2RDNCUC9RTWxKdC83Nlhwdm91RzR1SDFQNGNhaVRneHBJMEFDWExTSlJsVWtqeUExcGFjUDBaOEU0empFa2hvd2x3RWV2QnZzMnFUdGN4Z0ZNZS9NaFU4NnFFT2sxN216eXE5TStRK2Vpbmw2NGZmam8yUmtBUUhvRlBoS0x1VlhLdlpxemU3K2NVK0ZjUEM5MU5GTUtHcHcwR0ZIeWRIb09GQnBWTUE5eEtSZXJQYS9SOGM0VEJLckw0NGpxbm5LRDJMVkhTR2FGdmN2RnJqYmNkakVkMHNxK25qQ0ZjSUdwb3pHNzJpb3FCVEZjcFJTVWdqSUFBb3owWlJzYzk1NnpyTXpqTmk5NENlNE1KVDYwOWV2eERlRUpCb0FlZFIiLCJtYWMiOiI5OWNjMDA1MjYzOWI5NGE2OTk3Y2FhYTJmNTYxOWY3NmNjODcwNzZjYjRmYWI3Y2Q2NzlkYzNhOTQ4ODZlNmI5IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:19:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlJYdEFiQ2RtL29nWkF2UC9vWlhnQkE9PSIsInZhbHVlIjoiSC93eStyQ3hJUDFvbHozQjloRTcvcUVkeHdLUjc1UUxUZTNCWWZCdkIrVXFzdkJrSWVjRW5MdW5wOTBDazdMSk5Dbmh2eW9IeFFITG1mdklKbE5uTXFSL0RpbUpTc1grcG9BblZWTlVnRWE3M3dXMklPWXV4b3FLZHhzSm5YV093RjBOMHNYWUFPeHZBN1Z6RWRYUXpmeWdyWGJHSmVUZnlyRDl4RjhYYk5iSmZHWllENEtId3JNbU5YWkVQYWhEK2dGNEJKVG10NnFjejdWd3g2bmJpRGRVT29vcnRqYk5TRGJkL1RhREdzY0VyYjFkSXNxMEovbEl0MUdvSnBpaHFqZ0cxaEgyRTA0Y2VYaHFNNjdtYkhoSGtGSnNYbjJISm1pVndhR05vZCtSVFB5SXU5bXF0Y01HdTR1RDZFSE9BakpXaTFnMi9xNWV1V08xK3lrS1FyL2haenlueC81L3BuSFA1cGRMWERwdVlGcG9wSG1TT0YyeHA4dzdpNWhPSVVpLy9PSFBOOENEb1g5ZitCb0R5cFZXQWtBZDV2MHJ2MDFOcDhITUJNZmdNL2VqNVM3L3dMQkc0V1ZIS3BmeGpWaG9hY2FHTDFhMXZFSHVJRlo4bC9OZzQrclNUY285WGRGMGd6V2dkVXZ3bEpTZEVWejVyU3Y5bnNEU0REcHUiLCJtYWMiOiJmZGMyOTAyMzc2M2FkZTNhYTFlNmE5NzM4NWUyYzllMTFjOGM5OTNjMDEzZjk4NjIwZWJhNTk2YWE0NTUyMTdkIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:19:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1804502742\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1747778597 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1747778597\", {\"maxDepth\":0})</script>\n"}}