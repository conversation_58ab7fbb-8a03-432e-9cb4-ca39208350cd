{"__meta": {"id": "X58f50d93d9140323425cefdb89040790", "datetime": "2025-07-30 10:12:27", "utime": **********.763427, "method": "GET", "uri": "/finance/sales/contacts/lead/11", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753870346.843173, "end": **********.763476, "duration": 0.9203028678894043, "duration_str": "920ms", "measures": [{"label": "Booting", "start": 1753870346.843173, "relative_start": 0, "end": **********.664734, "relative_end": **********.664734, "duration": 0.8215608596801758, "duration_str": "822ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.664759, "relative_start": 0.8215858936309814, "end": **********.763482, "relative_end": 6.198883056640625e-06, "duration": 0.09872317314147949, "duration_str": "98.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46721760, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1310\" onclick=\"\">app/Http/Controllers/FinanceController.php:1310-1369</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.006449999999999999, "accumulated_duration_str": "6.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.728018, "duration": 0.004889999999999999, "duration_str": "4.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 75.814}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.743805, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 75.814, "width_percent": 11.318}, {"sql": "select * from `leads` where `id` = '11' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["11", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1336}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.750818, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1336", "source": "app/Http/Controllers/FinanceController.php:1336", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1336", "ajax": false, "filename": "FinanceController.php", "line": "1336"}, "connection": "radhe_same", "start_percent": 87.132, "width_percent": 12.868}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/11", "status_code": "<pre class=sf-dump id=sf-dump-130051239 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-130051239\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-369999895 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-369999895\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-3318992 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-3318992\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-313561281 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IlhmbDZuYjFSSUpwVS9ob3FsbzlvTWc9PSIsInZhbHVlIjoiQm9JRFVycWxQRVEybFJ4dGFWOHVSWkQyWkpycE1tdnhIUnZZVTJMSXlyY1k3VGVqVDNpVTU3T3ozMkhhK2xqWGtpcmQrYUZYNnRmUnI3cnh0NnB6U0U4bmJuOEdOMzJUY21NZVBabE9zOWRHK0hPYjYzKzdXcmJKbUVocUxKRmZFYlhsWTBYcEZUOFhEOWltYWpoZW84SGRKWktEVnZUdXJZTU1FT1B1dFBFcFNKSm91MTM1Tmsxc0RVUHcwSTdJNjIvOHIxb0owNDErdXpXWkc4QVZvOC9ZOTdNRFZINXNUanVCRy9JQmF3RjBqNll0c1hEazFxUkdXK2RaNGFoeVJQNW41TkVMZG1SeDNQVFQ2MEMvTEc0c09BdW9NVXpxdDhSbldlN1FNQ0dhK0xOM0xqanArL29QT0k4WUd5ZUxYL0gyd3piK3R5TmFJbjhVUVUwcC84SzU1S0tDMnhQYXB1S1c2aEo4ZmZCWUJNYWg2ODBQSVFwWHdONFhCVUZodGpReDNmZ201d2wwRVhrNEQ5Z1FuYWVodU1EcDM1WW83MmVtYUk1S3hFREw0dmE3TGJsK25kN2piNDdRbkJMMU85dmUwVEtHREFCYjRGeGtnMVlSa2t3a3NqOXVEVEg1L0lvNlZIZkY2bC8yKzArR3Y4VmNtdkx5QU9YSlc2VXQiLCJtYWMiOiIzMmFjNjZmNDk5OWJmYjE3ODIwMzc4OWMwZWIyYzdhMzA5NDk5YWVmZjk3MzBiZmMxMjZiMjVjYTJkZGY0ZjlmIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImQyZ01YUWZWOWJGdU1GS0toaWl0M1E9PSIsInZhbHVlIjoiaS9HMFYyT3Y5RVBWTEZpbHY1eFJqTHpESldROFFlc2FjMG0zY2hVdVp2cDdTRnFTSjlVV1B2SmRiYXhjM2VKelplQnFMYTY1T0h0Rys0cWJ4ZzY4TkdvN1g4bHRpMUlhNUk0MTlUUzdGanl4TFpYRStmdE9oeFMzS2hYSnlaMmU0ekRwSURvSW5GSUlzR1U3VTBjaGZ2VTZ6T2RUK0JmcnJmeGpDTGpNKzJpQnNaL29abmdQNFp4MTE2bzdUTlZQR1dJVFpxQkZSR3kwWGZRNkpIZml1aUxnc0lteXJmeWc2WDVlZ2ZQSWdrMThNMnRsRHdsUDF4VFJQZUZqV1YxYnNmeVBKT0VabS9rcFJydS9tNFFMTUJzUWMwVHZMdk92MnZHaGFSV2dvaFFFNXVVQ3h1NTVaTnkwZE96c2w5QUU2MUtFY3VwbTZqNCtBeWZVOUlyZDBkdnVBd2lpMWtWS2VhTGdKb05pOTBlVGRqdGlrRVlQMms4K1JmbE5uRkVack5zYUdkak8vWUxxTHhac2ZOemlVVC9ndGlqZThKcXRBd1AxcllLSm5uTTBKQk5GZFZEc2U2SUVPeklKOXExNXJRSnEyRWsvVWRISXZTYkFWOCszOWtOYVEwT3RrY1V1UWZJcDVYZHZ2NGRmdXFvdnhTRzJHOHM4bjFuaEo4MFMiLCJtYWMiOiIwMjc4ZTg3MDJjMDk1OTQ4NTcwZjZhNDQyZDZlZGM5YzZjMDA5NGU2MWE1MDBmNDI5MTcxMDFjZjY1YWJjYzI5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-313561281\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1588686181 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1588686181\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-744638015 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 10:12:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxlQlUzaTVoamlOU0JyeDUwejVZclE9PSIsInZhbHVlIjoiTWdJZEtLbURwWHFPVUpsSy9ObEp4TlJwOUJSYkNrNFNCRFlwbGZmOGlPV1NNK3J6WXY0ajFWNUs3SndqVEdINUIvRlNEN3FsNXJWU3dNdFk5Y1JGeXpMRVBYcS94L1M2K1RuMURNZVgxTUtHVWxMUi84L1EyZE02NFZXOU93a3dRbVE3T00wcUo1VG5nS2lZU1lRalBYMUlrNzRsUkpnenF2Z0JDaWF2bWNCS0gwVkhXdjMyaHV5NHhvZUlVZmErVjBBcmp1cEw3T2RvUS9PTjdVWitaOElvclgrYVNaNmlZZFYvbDdZMFoxcEY3VC91djN1ZS9Lckd5QkF6QnJLOHM5cTk4TUtkRGtYM0pXWjFsemsyUmlQZlZQSk9USHh4eXppVm8zalFUejRxNXJuL1lBd3dmaVlTNnpvUUI2NExoSDVzU01RV2RHdi9iQTFZR1NzeWh4R1NSQXFGZnNVZW5VYVQ0SEUvbDIrWkVHTXRWd0xKOEowQUNjeS91VWdqVVVpQ1cwWUx0eVhWWm5sZjFNQVYzY3hZcWhVandPZktMbVRUQTVtVkIxL21BWGo4Ry95clpVYVBvMjYwVjVPK28zSmJxNEdPNnJsSS9sVnVHdnVqckI5THBUTG0yT05DSmsxaWNCbFlXaDNYVlg0SkQzdk52NExUVmlqVjdTQmIiLCJtYWMiOiJhZTRhMjk0Njk4NzQ2MGI0NDU5YTMwMjg2NjFmOGMxZGRmZDJhNTg3OGMyNzA1MGMyNzFlM2UxNTliYzBmNjZmIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:12:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImhnYnRVcmtIV0tpTThJdFVLY21relE9PSIsInZhbHVlIjoiOGlicFpKL3l6N0RPT2JaWDRiOFVUb09xUTFRcEQvU1BlUllCN2VPT1l3aXhNMmRndUxWN2dFd0crSFlzb205Z3p1M0JqZ0dyd2p2NXdJOWN4YUZlMXFrZWRsanQ0Qm1vNkVFeWwwM3pIbEJjb0M4SmNNdmVMOUtaSWhONmxrYitXUHIrWE94TG5jUEJ6L2xmSC9vR3RyOFNWWmVRZ2taYnc0dVdLNEZaMmJqbEYwWG1qakhOdUphSzFzbDhjVGxTT1pJQnBzNVVCbnBKTUxlUEtEZHRrQ1hBVFZabXpMREFUbVlaUEh1eTVYR1hiSFVLZFJrUm5WaDVtNGZNZno3OTVrSUU0VFJJRjBtMU10MGx3VVp4dHpFVFpSVlFQa0t4dlNaNDFteXpJdEpZS3NtU3hISm5VS2tPa3dwZDVla1RncU1icXhzOW91TEVkUmN5S24rL3BSWUVQbjlUd3ZxZ2lKSG4rdmVyUEF2dXBFdXJZNElyeit4WkFYNG12bFhUUXBVWjdXV3RkVmszaHBGZGJ3V3BxdlRoRjVrbXYvYnZqaEJEdHVFQ0M0aXcyWjJ1UEVoa1dqUXFabkx6b25mWFhva0FmRXJXaXZtZEt5b2VlT0FHaUxiRDNham9OSnM5cDk2ZGZtYWFUT2ZIaUwydysrSFN6WU9jNHlXS0h4R3oiLCJtYWMiOiJjYWJlNjllNTY1MjdhZjZjZjBhYWZiY2YyY2I5MTRiZjM4NmNmNGEyNDRiN2ZlZmU5OGI0N2ZlOGQ5MjdjMWQ5IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:12:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxlQlUzaTVoamlOU0JyeDUwejVZclE9PSIsInZhbHVlIjoiTWdJZEtLbURwWHFPVUpsSy9ObEp4TlJwOUJSYkNrNFNCRFlwbGZmOGlPV1NNK3J6WXY0ajFWNUs3SndqVEdINUIvRlNEN3FsNXJWU3dNdFk5Y1JGeXpMRVBYcS94L1M2K1RuMURNZVgxTUtHVWxMUi84L1EyZE02NFZXOU93a3dRbVE3T00wcUo1VG5nS2lZU1lRalBYMUlrNzRsUkpnenF2Z0JDaWF2bWNCS0gwVkhXdjMyaHV5NHhvZUlVZmErVjBBcmp1cEw3T2RvUS9PTjdVWitaOElvclgrYVNaNmlZZFYvbDdZMFoxcEY3VC91djN1ZS9Lckd5QkF6QnJLOHM5cTk4TUtkRGtYM0pXWjFsemsyUmlQZlZQSk9USHh4eXppVm8zalFUejRxNXJuL1lBd3dmaVlTNnpvUUI2NExoSDVzU01RV2RHdi9iQTFZR1NzeWh4R1NSQXFGZnNVZW5VYVQ0SEUvbDIrWkVHTXRWd0xKOEowQUNjeS91VWdqVVVpQ1cwWUx0eVhWWm5sZjFNQVYzY3hZcWhVandPZktMbVRUQTVtVkIxL21BWGo4Ry95clpVYVBvMjYwVjVPK28zSmJxNEdPNnJsSS9sVnVHdnVqckI5THBUTG0yT05DSmsxaWNCbFlXaDNYVlg0SkQzdk52NExUVmlqVjdTQmIiLCJtYWMiOiJhZTRhMjk0Njk4NzQ2MGI0NDU5YTMwMjg2NjFmOGMxZGRmZDJhNTg3OGMyNzA1MGMyNzFlM2UxNTliYzBmNjZmIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:12:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImhnYnRVcmtIV0tpTThJdFVLY21relE9PSIsInZhbHVlIjoiOGlicFpKL3l6N0RPT2JaWDRiOFVUb09xUTFRcEQvU1BlUllCN2VPT1l3aXhNMmRndUxWN2dFd0crSFlzb205Z3p1M0JqZ0dyd2p2NXdJOWN4YUZlMXFrZWRsanQ0Qm1vNkVFeWwwM3pIbEJjb0M4SmNNdmVMOUtaSWhONmxrYitXUHIrWE94TG5jUEJ6L2xmSC9vR3RyOFNWWmVRZ2taYnc0dVdLNEZaMmJqbEYwWG1qakhOdUphSzFzbDhjVGxTT1pJQnBzNVVCbnBKTUxlUEtEZHRrQ1hBVFZabXpMREFUbVlaUEh1eTVYR1hiSFVLZFJrUm5WaDVtNGZNZno3OTVrSUU0VFJJRjBtMU10MGx3VVp4dHpFVFpSVlFQa0t4dlNaNDFteXpJdEpZS3NtU3hISm5VS2tPa3dwZDVla1RncU1icXhzOW91TEVkUmN5S24rL3BSWUVQbjlUd3ZxZ2lKSG4rdmVyUEF2dXBFdXJZNElyeit4WkFYNG12bFhUUXBVWjdXV3RkVmszaHBGZGJ3V3BxdlRoRjVrbXYvYnZqaEJEdHVFQ0M0aXcyWjJ1UEVoa1dqUXFabkx6b25mWFhva0FmRXJXaXZtZEt5b2VlT0FHaUxiRDNham9OSnM5cDk2ZGZtYWFUT2ZIaUwydysrSFN6WU9jNHlXS0h4R3oiLCJtYWMiOiJjYWJlNjllNTY1MjdhZjZjZjBhYWZiY2YyY2I5MTRiZjM4NmNmNGEyNDRiN2ZlZmU5OGI0N2ZlOGQ5MjdjMWQ5IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:12:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-744638015\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-966964382 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-966964382\", {\"maxDepth\":0})</script>\n"}}