{"__meta": {"id": "Xb699a4472bbddcea15d519fd14b0fe84", "datetime": "2025-07-30 09:53:35", "utime": **********.070019, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753869214.224343, "end": **********.070046, "duration": 0.8457028865814209, "duration_str": "846ms", "measures": [{"label": "Booting", "start": 1753869214.224343, "relative_start": 0, "end": 1753869214.971187, "relative_end": 1753869214.971187, "duration": 0.7468440532684326, "duration_str": "747ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753869214.971202, "relative_start": 0.****************, "end": **********.070048, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "98.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1856 to 1862\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1856\" onclick=\"\">routes/web.php:1856-1862</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pkhxxPCgl3sCCexFuYuIgwe7hJ7EOsYFP50PNrIc", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-824966973 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-824966973\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-516803520 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-516803520\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1841318286 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1841318286\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1682424297 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1682424297\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1376848803 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1376848803\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1557077863 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:53:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlkwT1FSRzBzV2hzeFg5L1ZTNUtoS1E9PSIsInZhbHVlIjoib2crV29jNExZOU5iNnVRcitFZXdzc0xzdE9SV2hQVHFBUDQrZHFRMFhWbHF5cUluaU0yd05KaHRWZnZsN00xcHBPSS9pSUFJUzVGdWwzdFIzRGFmM1FraE05anl4YUlrNC84R2NFSzZHYnVNVUFIWGVSMzRrMW9XaHg1bkM2ME9CSlRUZ08vREF2b1E1Z3dmSkduREtLYkRYQkMvNk8zcjA2eUFyTnFrYldkUWtHZFEyLzNGZTdhd2dndFlEaGhQU25kdlhxUFljcDV3OUs4VDdOOGZoL3FENVhKNVJiTjNqUHpjSzZDSXNOUlcyRkJGdzBVVUZBVHNCRG5UTEpyMFBjWjRpWXpadnVscmZLN2lSRnp4Uk9iN0RYWmJZMW1sSDJ0Z0ZOVnpVU2FKN3RpSzd0dm51NXU5K2xvbU0vV3ZMdjZ4V1IxVFpaUHZLZ1RDL0s0YjdPcW9LakpXbFNXT0hicXpRYjhaY1FuQ0lUNTBLQUJvcnlBTFZTMy9mcTVOZldTL09McmYvcDEzTlJmTWx3TjNuNXArRDBQKzFXR0tDdlMyVHFTZHQ2U29XZGlKQS92QzJUOVBwOUFJNnhnUXRmQm83VTV1ZExnRm1SS2ZzYlFWNnM4MERhaWpGd3ZCRmJrdU42QzgwU1JBRlVuREpMRVp0SXBiSk52RnI1aTUiLCJtYWMiOiI4YzkzMDRiYmQyNmJiNTg1MWNiMjY5NjZmMjJjZDcxNjBmN2ExYjY2OTM2NjljYmI0ZWIxMDE3NjE4MjlkZTEzIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:53:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlV1NnozTXJkbXRqRTB1UnJUUXIyZHc9PSIsInZhbHVlIjoiNDg4V2lXckhCbVVUM1lZaWhxOE56TGt0MkNHMTNTNktLOFV3TzB4VCtUeU9pTmFZLzVLRENIbFlYWmJ6Q0FRaUtxYnlLaVFvNmJDb1p5UFF6WGhaekE2dmtvY1Z5TVJ2K245a3BGeEw2ME80cldybFZCNjF0dDFFeEF5RXg5RzJhbWxIeVpTaHhvenBTbC9jaEd5cVpFclVNa3gzc3RyN1FZSXRVYXlROXVDbWpFbG5Ea0t6blg2UXN1RklPK2xYNUtuaEJtdEdpbWlJUWRiSE40bFVMZVMzRXJDakRZdVdqdGxOUVhaNkhPdDlaVVNMZnA1SFlyTEN2WGRlbVhadUdIbDJHQWZVNnU5WTNzOGMyMzhVUmxNWkNteDhPSXVGNXViTkh2V1B6U2RmbHVkdWJidzd6bllma0E0WmFybXQ0NjN4b3dHZlNlWXIxZmJEKzRzQlRNSkZ1V21UKzAyWW5vTjV3Nko0ZjNPUkJYYUFEZnZWTWRPcUcycnlybkZMY3lRb2RRWmNYbHhkZ3k1UzhlYUVnNnpaeThVemt5endWK2lZMGpNVi9wdTZMQjJkUWtWdk1ZaUNWeW5oWis2bDNnZHhpWThrSTI2ckdlWmJXR3pFY0l6V2lqOVVqejVCNGo1dEMyRGU3ZWJrOHkzVnY4cGNTNFhjVTBaV001YVMiLCJtYWMiOiI0ZmY5ZDhmNzc4ZDljNDU5MmI2MTMxOTNlNmRlZjhlODIzOWZlZmYyOWE1Y2JmOTRiZWM3MWVjNzg5OTE5ZDcxIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:53:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlkwT1FSRzBzV2hzeFg5L1ZTNUtoS1E9PSIsInZhbHVlIjoib2crV29jNExZOU5iNnVRcitFZXdzc0xzdE9SV2hQVHFBUDQrZHFRMFhWbHF5cUluaU0yd05KaHRWZnZsN00xcHBPSS9pSUFJUzVGdWwzdFIzRGFmM1FraE05anl4YUlrNC84R2NFSzZHYnVNVUFIWGVSMzRrMW9XaHg1bkM2ME9CSlRUZ08vREF2b1E1Z3dmSkduREtLYkRYQkMvNk8zcjA2eUFyTnFrYldkUWtHZFEyLzNGZTdhd2dndFlEaGhQU25kdlhxUFljcDV3OUs4VDdOOGZoL3FENVhKNVJiTjNqUHpjSzZDSXNOUlcyRkJGdzBVVUZBVHNCRG5UTEpyMFBjWjRpWXpadnVscmZLN2lSRnp4Uk9iN0RYWmJZMW1sSDJ0Z0ZOVnpVU2FKN3RpSzd0dm51NXU5K2xvbU0vV3ZMdjZ4V1IxVFpaUHZLZ1RDL0s0YjdPcW9LakpXbFNXT0hicXpRYjhaY1FuQ0lUNTBLQUJvcnlBTFZTMy9mcTVOZldTL09McmYvcDEzTlJmTWx3TjNuNXArRDBQKzFXR0tDdlMyVHFTZHQ2U29XZGlKQS92QzJUOVBwOUFJNnhnUXRmQm83VTV1ZExnRm1SS2ZzYlFWNnM4MERhaWpGd3ZCRmJrdU42QzgwU1JBRlVuREpMRVp0SXBiSk52RnI1aTUiLCJtYWMiOiI4YzkzMDRiYmQyNmJiNTg1MWNiMjY5NjZmMjJjZDcxNjBmN2ExYjY2OTM2NjljYmI0ZWIxMDE3NjE4MjlkZTEzIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:53:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlV1NnozTXJkbXRqRTB1UnJUUXIyZHc9PSIsInZhbHVlIjoiNDg4V2lXckhCbVVUM1lZaWhxOE56TGt0MkNHMTNTNktLOFV3TzB4VCtUeU9pTmFZLzVLRENIbFlYWmJ6Q0FRaUtxYnlLaVFvNmJDb1p5UFF6WGhaekE2dmtvY1Z5TVJ2K245a3BGeEw2ME80cldybFZCNjF0dDFFeEF5RXg5RzJhbWxIeVpTaHhvenBTbC9jaEd5cVpFclVNa3gzc3RyN1FZSXRVYXlROXVDbWpFbG5Ea0t6blg2UXN1RklPK2xYNUtuaEJtdEdpbWlJUWRiSE40bFVMZVMzRXJDakRZdVdqdGxOUVhaNkhPdDlaVVNMZnA1SFlyTEN2WGRlbVhadUdIbDJHQWZVNnU5WTNzOGMyMzhVUmxNWkNteDhPSXVGNXViTkh2V1B6U2RmbHVkdWJidzd6bllma0E0WmFybXQ0NjN4b3dHZlNlWXIxZmJEKzRzQlRNSkZ1V21UKzAyWW5vTjV3Nko0ZjNPUkJYYUFEZnZWTWRPcUcycnlybkZMY3lRb2RRWmNYbHhkZ3k1UzhlYUVnNnpaeThVemt5endWK2lZMGpNVi9wdTZMQjJkUWtWdk1ZaUNWeW5oWis2bDNnZHhpWThrSTI2ckdlWmJXR3pFY0l6V2lqOVVqejVCNGo1dEMyRGU3ZWJrOHkzVnY4cGNTNFhjVTBaV001YVMiLCJtYWMiOiI0ZmY5ZDhmNzc4ZDljNDU5MmI2MTMxOTNlNmRlZjhlODIzOWZlZmYyOWE1Y2JmOTRiZWM3MWVjNzg5OTE5ZDcxIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:53:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1557077863\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-297566628 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pkhxxPCgl3sCCexFuYuIgwe7hJ7EOsYFP50PNrIc</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-297566628\", {\"maxDepth\":0})</script>\n"}}