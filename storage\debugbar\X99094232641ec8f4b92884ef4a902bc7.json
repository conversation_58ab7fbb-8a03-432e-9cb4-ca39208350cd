{"__meta": {"id": "X99094232641ec8f4b92884ef4a902bc7", "datetime": "2025-07-30 09:25:35", "utime": **********.306229, "method": "GET", "uri": "/finance/sales/products/search?search=shi", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753867534.245136, "end": **********.306251, "duration": 1.061115026473999, "duration_str": "1.06s", "measures": [{"label": "Booting", "start": 1753867534.245136, "relative_start": 0, "end": **********.13975, "relative_end": **********.13975, "duration": 0.8946139812469482, "duration_str": "895ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.139777, "relative_start": 0.8946409225463867, "end": **********.306253, "relative_end": 1.9073486328125e-06, "duration": 0.16647601127624512, "duration_str": "166ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46676680, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/products/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchProducts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-products", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=905\" onclick=\"\">app/Http/Controllers/FinanceController.php:905-946</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00555, "accumulated_duration_str": "5.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.255916, "duration": 0.00453, "duration_str": "4.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 81.622}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.289002, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 81.622, "width_percent": 10.811}, {"sql": "select `id`, `name`, `sale_price`, `sku`, `type` from `product_services` where `created_by` = 79 and (`name` LIKE '%shi%' or `sku` LIKE '%shi%') order by `name` asc limit 50", "type": "query", "params": [], "bindings": ["79", "%shi%", "%shi%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 923}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.293804, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:923", "source": "app/Http/Controllers/FinanceController.php:923", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=923", "ajax": false, "filename": "FinanceController.php", "line": "923"}, "connection": "radhe_same", "start_percent": 92.432, "width_percent": 7.568}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/products/search", "status_code": "<pre class=sf-dump id=sf-dump-1041864985 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1041864985\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-685992381 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"3 characters\">shi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-685992381\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-674373238 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-674373238\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1388240758 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImtYVC9Zcmd2WEZOeXpwd3o0WDFmZGc9PSIsInZhbHVlIjoiMlFDZmV5Z0tPOElqVW4zbzB4amUxTnVaUVI1Ky9aYk1JaWV4eHdpWlE5amRjQkJGTXJTUzYzSi82ZEFrd3FCVzJoUXVWTjZiWmlFalFSdDdkTnZPenBvRXZVUlJ2cDRyMS9IUlA4OHFneEhuRkxhUnhOalV1Tjl0K003cmFoNnV4L0JpYXlOZE95MzliSE9uVVdnNituNzNFTXNDV1FIcWVhQnhBRVpVTGh5ek5sMnkybmNUcEdhYWZITmVmRXg4YmhDcHlMaXFMMzhiWm5ua2hTZHNsSzI4OVZmeXl1REpoRUM3UU1HSUJNTEtlck96aGFhemw0N3gwSE8vYzlwVGprNytjRmlPaEgxWVJVeEphKzN5YXNZRk11OU1ybzMzamVpWHZoMkp2UlBOZEZSOVhHMUM4cXR1ZG5hbE04QmVXUGRiZGhpajNYSkxoM3BGNnU2RnZaNWNQOVdJQzhwUmw2THJJTlcvcWNoN3BjckttRXBhaS9pVGw1QU1Hc0xXcVVJMWVuYzNoUkZSL2xobUMxR1lTY1k1ck9TTmxvVnNXczd0bmN2eHJBYWk3Zy9DRmh5aWYvMFY0d2hJV3d6REdpcjRGbDdoTklKZEkra1FGZ3AzTGswUncyaVVVTU1NUDJvejRXNlp4SzMwT1JhN01IS0YvMEFPeVZCVnFTMG8iLCJtYWMiOiIxMzJmMzNlZGFjZmZmYjUwNjQ4YTRkY2Y2YTEyNmEyNDhjNTMxMDY3NDE0ZDIwZjAyOGQ0YjU4MDFkZTI0OWViIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkFqRW5FQ1ZrY0JNNldMdTM0WWdldEE9PSIsInZhbHVlIjoic2NlalRLRDY0YzJEMkUwZllPTzdBVzVQTS9BbXBiV25tUVI0cHJTL2JsV1JFb2dJY0VERzM1ejRQRGxXUXJFemVzeFdESnptcTJUZ2Mvcmp3dEI4SkhMRkEvQk1Zczk4N24vWHV3UjBrZTRzNk5hbUZQd1VVNWZiQ1EyYldLeWprRFVSRXhnWHVpQW5lNnp2WjRpajRUak0va3IwVGNMbWlyMTVmdkJZdzhMWVVrRVY5dkg3WDlrZVc1YnNEdzJQb3FqTXRPQTlObkFpZE9CSU5pakZnN3czUGQzbmJnVFduZ0lTSWtjUUdwN0JTRkVWQlRQT0lYQ1BWbmc3eERub0d6VUl5Q0l4UUtVYjgwZmRRY3B0THppVzVVSFBlM1JpazNteDFud3dnUDRJNkZBSU5rMXlFTTVOYUdjZGJvYm1LQUJQNGZ4RksveGJGWGxQTnBxZm8ybHd1bUZLM1daZ01vZS9OdWs3L0pvV25CZ0xObjY2UkRFbmE3cmVGcW5ML1JkV2VLcEFqRnhab0NSb200TlYrZDJkN2c5cEJoVkxkbGt4eUJhMlNLMDdaTitRdDE3ZmVLQzF5aUpuSGtQTzJJbHdScFhSbm9NWktZTEIyc1pZSVlmNVRveGhaeHQ1TFI0TnZGaDFWVm1lTlpZbFlUTk5maWVCQmdOcjh3dHciLCJtYWMiOiI4NjczZTFhNTg4YmU5OGFjMzY1MjI1MzE5MGQ2NTA2ZjcyYjYxMzc5MGJlZTI4OTc3ZGNjODBkZmVmOGFmMjcxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1388240758\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-260810199 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-260810199\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1926917519 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:25:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdhSmtCckpwczA0aUxMbGdCYXhrakE9PSIsInZhbHVlIjoiZ2hUSHpxTkY1YmJ3aVA1VktzaklKN0lPbkZHVEJyZW4xSzZJWlY3ZkJhd1FFSHBNWEs3NjhHN3loYzFWZ3l2TXpwTG1JZ0xnUkRRVW9FSnBkaWdPdUJIVk5CbjdCNDZ2Z1FPOW1UOW4zckV1RlROWXVJdHc2VFZyRis5L3U5TS85UFhtTlZ0b2h3ZjlGc1NZNjFsODVOVlQ5N1lrZHNOWjBlY0FBR0pmZG9nUWJyOTFtOVRXdEE5U0FxaC92K2w0bnVqYmlJZmkzUVAzaG4vWEQzQS9kVGI2SjNLTkFFQ2lDNlV3Umt3RG1sK3JNeEtYREtkMnVGak42eU9WRXlybkQ1ektqYVlPQzR3U0VKU3ZCWk9STVhGU2tBS2x0K3kySGdlQ3RyVzRPTXczcXIxUk9COHB1Qm8rNjJJVDFiSUhONlp1clJFRS9xQmZoTU1CUVkxS0hVMkh1MVRXMmZCUjlXMkhxQ01idjl4QUhlMDRiMHNLUFpXUDB0K1psQzV4WmxLSjJpZytzN280YVNoeEY0c0xaZ1cwUWQ0REIvL295RmVSSTV5NXdkMVN4d1YyWGt1eHdkdHB1VlFjSGlrNTRuVlVobnc0enBYSXVCdlZxdXYzUjFPNmI3RHFxRXRmYVBGNXgrbDFvRlZqaHpOeWFEVmZhYThzZDQ2QzdQMnUiLCJtYWMiOiI2MzNmMzU2MzBlYWEwNTIwYWI4OGNhMDZmN2RlNjljMDI5Y2Y2Zjk2OTIxMzgyMjQ4NGE0OGQ1ZjMyNmUxY2RlIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:25:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ikg2cW9IVzBKUFdIT21ZV2hWWmpkbWc9PSIsInZhbHVlIjoiSStCdGN4Rlo3dEloVkxWbW9VL0NwY3NZdkVodkFnbngvZUQ4NWg5L3YwMExqSFRYU2xOK1h5SWpOVHdYcFp1d1VmRkdGbE1KeThWYnprak4wRUxaeTUwa1N1SllNSXd3aEFNdlM0UmY3eThSQlpMTWdIZUM4b0JvYVFDbmc2UGw1VWVMc05DV1FxWW9MUWpTQ05zQlFYUlV4eGM4dGRFYkNrd1BkWlNNeVBBcDJ0NUU1cDVkWWlnSG9RV0pQdXRjaGNUOFhFWmwrcG5POERYM216WFkyUmV0NTJIMi9hMzlVMUNZZlpZZWRFY2dDc21TSGJyWHlQOE1UTS9jRExkQmRpdjVvOVY2UEszRU5rMFZpU1Fzc2grM0YyYWdjNFM3OCs2UHpBSU1WSVppYmV5QUZNVmt1c2J6S2ZvS3krc0RVTUhmT1hRRDdsZkM5NjNWNUwyTGlyaklVNVpyYWV4ck9MWWRncnZIZHNhYWovbUdJTGhRSEIzK3BWRUR5N1l2ZUxuaXNaSlEvenFqSk9yYzFPVzdMV05tZFZ0KzJpYTFHWGlSYVNHNlpEWG5NbE1DRmN4dHZiSlQwdlYrTm5POWlCd3RnQU1YZU4rMlE3VE5RVWhkcmlYb2lBSnVwNGs3STVTaDBCY00zYlJ3T3lmcDRJNnRYdXFSZlBFczZ1QWoiLCJtYWMiOiJhYmUxZWZhOTc3MjYwNWE5MzIxZmQ4N2FmZjg2NGFkN2E0Yjg5MjY4MzNlMzhjZDlmYThiM2U3ZjcyNWY5YmVhIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:25:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdhSmtCckpwczA0aUxMbGdCYXhrakE9PSIsInZhbHVlIjoiZ2hUSHpxTkY1YmJ3aVA1VktzaklKN0lPbkZHVEJyZW4xSzZJWlY3ZkJhd1FFSHBNWEs3NjhHN3loYzFWZ3l2TXpwTG1JZ0xnUkRRVW9FSnBkaWdPdUJIVk5CbjdCNDZ2Z1FPOW1UOW4zckV1RlROWXVJdHc2VFZyRis5L3U5TS85UFhtTlZ0b2h3ZjlGc1NZNjFsODVOVlQ5N1lrZHNOWjBlY0FBR0pmZG9nUWJyOTFtOVRXdEE5U0FxaC92K2w0bnVqYmlJZmkzUVAzaG4vWEQzQS9kVGI2SjNLTkFFQ2lDNlV3Umt3RG1sK3JNeEtYREtkMnVGak42eU9WRXlybkQ1ektqYVlPQzR3U0VKU3ZCWk9STVhGU2tBS2x0K3kySGdlQ3RyVzRPTXczcXIxUk9COHB1Qm8rNjJJVDFiSUhONlp1clJFRS9xQmZoTU1CUVkxS0hVMkh1MVRXMmZCUjlXMkhxQ01idjl4QUhlMDRiMHNLUFpXUDB0K1psQzV4WmxLSjJpZytzN280YVNoeEY0c0xaZ1cwUWQ0REIvL295RmVSSTV5NXdkMVN4d1YyWGt1eHdkdHB1VlFjSGlrNTRuVlVobnc0enBYSXVCdlZxdXYzUjFPNmI3RHFxRXRmYVBGNXgrbDFvRlZqaHpOeWFEVmZhYThzZDQ2QzdQMnUiLCJtYWMiOiI2MzNmMzU2MzBlYWEwNTIwYWI4OGNhMDZmN2RlNjljMDI5Y2Y2Zjk2OTIxMzgyMjQ4NGE0OGQ1ZjMyNmUxY2RlIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:25:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ikg2cW9IVzBKUFdIT21ZV2hWWmpkbWc9PSIsInZhbHVlIjoiSStCdGN4Rlo3dEloVkxWbW9VL0NwY3NZdkVodkFnbngvZUQ4NWg5L3YwMExqSFRYU2xOK1h5SWpOVHdYcFp1d1VmRkdGbE1KeThWYnprak4wRUxaeTUwa1N1SllNSXd3aEFNdlM0UmY3eThSQlpMTWdIZUM4b0JvYVFDbmc2UGw1VWVMc05DV1FxWW9MUWpTQ05zQlFYUlV4eGM4dGRFYkNrd1BkWlNNeVBBcDJ0NUU1cDVkWWlnSG9RV0pQdXRjaGNUOFhFWmwrcG5POERYM216WFkyUmV0NTJIMi9hMzlVMUNZZlpZZWRFY2dDc21TSGJyWHlQOE1UTS9jRExkQmRpdjVvOVY2UEszRU5rMFZpU1Fzc2grM0YyYWdjNFM3OCs2UHpBSU1WSVppYmV5QUZNVmt1c2J6S2ZvS3krc0RVTUhmT1hRRDdsZkM5NjNWNUwyTGlyaklVNVpyYWV4ck9MWWRncnZIZHNhYWovbUdJTGhRSEIzK3BWRUR5N1l2ZUxuaXNaSlEvenFqSk9yYzFPVzdMV05tZFZ0KzJpYTFHWGlSYVNHNlpEWG5NbE1DRmN4dHZiSlQwdlYrTm5POWlCd3RnQU1YZU4rMlE3VE5RVWhkcmlYb2lBSnVwNGs3STVTaDBCY00zYlJ3T3lmcDRJNnRYdXFSZlBFczZ1QWoiLCJtYWMiOiJhYmUxZWZhOTc3MjYwNWE5MzIxZmQ4N2FmZjg2NGFkN2E0Yjg5MjY4MzNlMzhjZDlmYThiM2U3ZjcyNWY5YmVhIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:25:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1926917519\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-481745461 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-481745461\", {\"maxDepth\":0})</script>\n"}}