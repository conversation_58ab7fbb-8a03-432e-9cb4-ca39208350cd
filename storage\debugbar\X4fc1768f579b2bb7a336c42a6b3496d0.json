{"__meta": {"id": "X4fc1768f579b2bb7a336c42a6b3496d0", "datetime": "2025-07-30 10:32:26", "utime": **********.415055, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753871544.383594, "end": **********.415102, "duration": 2.031507968902588, "duration_str": "2.03s", "measures": [{"label": "Booting", "start": 1753871544.383594, "relative_start": 0, "end": **********.161314, "relative_end": **********.161314, "duration": 1.7777199745178223, "duration_str": "1.78s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.161467, "relative_start": 1.****************, "end": **********.415107, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "254ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3038\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Lrik2HJWZ4nDloBE9SvQFtij28VBLNKcepf9APXp", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1339466841 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1339466841\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-482583713 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-482583713\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-876250641 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-876250641\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1237642839 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1237642839\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1764355965 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1764355965\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1974870226 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 10:32:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNVWXpCNmRTQUdhQ1ljL25OSVdQRlE9PSIsInZhbHVlIjoiNjJBQzlpU3RoQ2JBV3NRV0ZIbUhOR3pCRlRnWTBzK3RtNGpIY21tVG9QRHNOcTIrZmU2ZVZZRUtqb1lXNUJFSVhkTUVtRkZKNzFIajZoNHN1S20rK1dOdjU1SkFiWmhNRDVCWEh1ZU14ZVZ5cFFmVHQ1N2dWdERPMEFOcnR6YStVeVoxRHdoWTVwZnVWMGRqVmYwSi9xc0JVeFZhRUhXVTVoU3JUbGlOSzRuSjlOMUQ4YTR4eVFMZkNKWWNqWGNObHlVL203TUgrSG1nMks5QWZNQ0NJVC9RbEJBOWp5Z1Z3dkowd3hTTGNoSWw0YTNKWHZUUFYvekZQaWFZck5tVXJ5WnZFYWkrSTY3QzZOMURDbkVKNExGcTNPUWt1Q0JZQUw2YVRhM1EzZFRUc1Z4QU5MOERJTTR1bXZ5bXNEcnd0S3BJTkc0VWRkeG5mVWZkTVNlRUs3NzMzUUREcWZIa0Rpakt4NmVjUzVSQlJkKytHdGVpOXl3UUVPd0tjdVpsVTZFck1zOWM2MHRJb3F5dFU0UlVjS3NtWkMwaXliYkJ6MURiUjdUTXo0SmhrbE5Od095Y3VCbFU3SFNmaVdHYzAyVzdSS280U2JoUTlVSTJOV2xISG9saHRmcXpMKzhRcHdpV0hjUUxqY0tsU1g5c2FPT0x6UEhXdHpoZmVBVmsiLCJtYWMiOiJlMDdmNWY4M2NkYmFjMmUzMmQ3Y2Y1OTU2NzJkOTAxOTdlNGY3ZmY3YmNiZmI3N2ZkMDMwZTZjNjNlN2IxMDBkIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:32:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik9rUVRDL3lTUHZIZGxOL3I1Vm5kR2c9PSIsInZhbHVlIjoiUk5PUWVYRzdXTytuTE81TUUvcE4xTVZjMnE2MVhpMmdFL1BuOGsyMHcrZnJoU0x5MGZ2bmxDd21tQnYrN2w0b1ZqV28xY1c0dXhsSmgySWhQeSs3d3NnZFBJZzZUZW9kSWtoSGRvVFdmcG9UTXhXRXJsUy9BSTdkN1p6RVJia0ZGays0bkYvcFpQSnVPYlZMclhiZWRXUmNyemc0Y0JaZHV0aHFYSFgrUDlTa0VFaXc3bkZ0SU9KSzE2SVpMWkFHbHpsRkJ0d3oyUThrQlZHK1E4OElFSktZS282K0kwNkFFMjZWVE9hQWoyNm9rK1Nyci9ZV2QzQTFRVkFRdXRlenpLaXJObXAwaVVaSmlkb0lzZ3dYVkJFblJGNGhIeUVQY1IxM0lZZVI3SDVuSzFCMjI1Z295a3dWeWMyMVFkU0NGMjlWNXNWL2hXcWdFRmt6OHZqNXZQVFhVOGlxRWdMQjhUaGRGTGxyRXNZNHlEUGM2TW5YNjNJRW01M1NueVRPVkt5WEUwc0Z0aHE4bnN0eWhiMExpcTVXMnk0OG5iM0RuNkVSUmJOcWZsZ29JWDF6VEhZdGwvLzhCQ1ZVYTRnMjZReFRocVZmbnRsVXp5OERYblpnNGpjUDliOFVsU08vUjBwKzFpUDB5anZuUExMb1Y3SzlWVUl3YWhQNWtMc1kiLCJtYWMiOiJjNjE2ZmUyZWQwMDZhNzJjODM2NGM4NTE5MmNjODEzODIzYmE0NDQ2ZTA3YmUyMTI2Nzg4NTQ0NGM0ZTJlNGM4IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:32:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNVWXpCNmRTQUdhQ1ljL25OSVdQRlE9PSIsInZhbHVlIjoiNjJBQzlpU3RoQ2JBV3NRV0ZIbUhOR3pCRlRnWTBzK3RtNGpIY21tVG9QRHNOcTIrZmU2ZVZZRUtqb1lXNUJFSVhkTUVtRkZKNzFIajZoNHN1S20rK1dOdjU1SkFiWmhNRDVCWEh1ZU14ZVZ5cFFmVHQ1N2dWdERPMEFOcnR6YStVeVoxRHdoWTVwZnVWMGRqVmYwSi9xc0JVeFZhRUhXVTVoU3JUbGlOSzRuSjlOMUQ4YTR4eVFMZkNKWWNqWGNObHlVL203TUgrSG1nMks5QWZNQ0NJVC9RbEJBOWp5Z1Z3dkowd3hTTGNoSWw0YTNKWHZUUFYvekZQaWFZck5tVXJ5WnZFYWkrSTY3QzZOMURDbkVKNExGcTNPUWt1Q0JZQUw2YVRhM1EzZFRUc1Z4QU5MOERJTTR1bXZ5bXNEcnd0S3BJTkc0VWRkeG5mVWZkTVNlRUs3NzMzUUREcWZIa0Rpakt4NmVjUzVSQlJkKytHdGVpOXl3UUVPd0tjdVpsVTZFck1zOWM2MHRJb3F5dFU0UlVjS3NtWkMwaXliYkJ6MURiUjdUTXo0SmhrbE5Od095Y3VCbFU3SFNmaVdHYzAyVzdSS280U2JoUTlVSTJOV2xISG9saHRmcXpMKzhRcHdpV0hjUUxqY0tsU1g5c2FPT0x6UEhXdHpoZmVBVmsiLCJtYWMiOiJlMDdmNWY4M2NkYmFjMmUzMmQ3Y2Y1OTU2NzJkOTAxOTdlNGY3ZmY3YmNiZmI3N2ZkMDMwZTZjNjNlN2IxMDBkIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:32:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik9rUVRDL3lTUHZIZGxOL3I1Vm5kR2c9PSIsInZhbHVlIjoiUk5PUWVYRzdXTytuTE81TUUvcE4xTVZjMnE2MVhpMmdFL1BuOGsyMHcrZnJoU0x5MGZ2bmxDd21tQnYrN2w0b1ZqV28xY1c0dXhsSmgySWhQeSs3d3NnZFBJZzZUZW9kSWtoSGRvVFdmcG9UTXhXRXJsUy9BSTdkN1p6RVJia0ZGays0bkYvcFpQSnVPYlZMclhiZWRXUmNyemc0Y0JaZHV0aHFYSFgrUDlTa0VFaXc3bkZ0SU9KSzE2SVpMWkFHbHpsRkJ0d3oyUThrQlZHK1E4OElFSktZS282K0kwNkFFMjZWVE9hQWoyNm9rK1Nyci9ZV2QzQTFRVkFRdXRlenpLaXJObXAwaVVaSmlkb0lzZ3dYVkJFblJGNGhIeUVQY1IxM0lZZVI3SDVuSzFCMjI1Z295a3dWeWMyMVFkU0NGMjlWNXNWL2hXcWdFRmt6OHZqNXZQVFhVOGlxRWdMQjhUaGRGTGxyRXNZNHlEUGM2TW5YNjNJRW01M1NueVRPVkt5WEUwc0Z0aHE4bnN0eWhiMExpcTVXMnk0OG5iM0RuNkVSUmJOcWZsZ29JWDF6VEhZdGwvLzhCQ1ZVYTRnMjZReFRocVZmbnRsVXp5OERYblpnNGpjUDliOFVsU08vUjBwKzFpUDB5anZuUExMb1Y3SzlWVUl3YWhQNWtMc1kiLCJtYWMiOiJjNjE2ZmUyZWQwMDZhNzJjODM2NGM4NTE5MmNjODEzODIzYmE0NDQ2ZTA3YmUyMTI2Nzg4NTQ0NGM0ZTJlNGM4IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:32:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1974870226\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1447993429 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lrik2HJWZ4nDloBE9SvQFtij28VBLNKcepf9APXp</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1447993429\", {\"maxDepth\":0})</script>\n"}}