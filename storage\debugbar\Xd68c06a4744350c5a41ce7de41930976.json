{"__meta": {"id": "Xd68c06a4744350c5a41ce7de41930976", "datetime": "2025-07-30 10:22:00", "utime": **********.985864, "method": "GET", "uri": "/finance/sales/contacts/search?search=pa", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753870919.953271, "end": **********.985885, "duration": 1.03261399269104, "duration_str": "1.03s", "measures": [{"label": "Booting", "start": 1753870919.953271, "relative_start": 0, "end": **********.811895, "relative_end": **********.811895, "duration": 0.8586239814758301, "duration_str": "859ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.811926, "relative_start": 0.8586549758911133, "end": **********.985887, "relative_end": 2.1457672119140625e-06, "duration": 0.17396116256713867, "duration_str": "174ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46721216, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1238\" onclick=\"\">app/Http/Controllers/FinanceController.php:1238-1305</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01006, "accumulated_duration_str": "10.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.933297, "duration": 0.00791, "duration_str": "7.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 78.628}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.959419, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 78.628, "width_percent": 6.461}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%pa%' or `email` like '%pa%' or `contact` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1254}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.964675, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1254", "source": "app/Http/Controllers/FinanceController.php:1254", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1254", "ajax": false, "filename": "FinanceController.php", "line": "1254"}, "connection": "radhe_same", "start_percent": 85.089, "width_percent": 6.561}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%pa%' or `email` like '%pa%' or `phone` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1278}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9693131, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1278", "source": "app/Http/Controllers/FinanceController.php:1278", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1278", "ajax": false, "filename": "FinanceController.php", "line": "1278"}, "connection": "radhe_same", "start_percent": 91.65, "width_percent": 8.35}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1118846939 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1118846939\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1724453869 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">pa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1724453869\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-31845686 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-31845686\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1808721906 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImJqdFRucnBqbVFVTkdGNEE5YzBNZkE9PSIsInZhbHVlIjoidG9VcnBiK0hpS3FJek9SUkVaRVR5N2RDKzdqL1A0K3B1aW5ieFd3SkhhYXhPdU83cFVUVkdhOUwyNEtOQmxrdWJHSmlubWJNUlI5THVXZnBRUktBZExJeEhXanY5cEZ0MWwrZ24yc09iL0xuUDBmS2V3RjJjQVZKejRsZVJld25nZlRickI5SFNPZXhuYVM4dGpLbnVwSkxuV0hCT2RCcVhaWE44NHpRN0lraDU2OVljRDNsS3c2SWpCa1JHSmtmamhiTHJMNWk2WGZ4OTAwamZ0Z3NwL052K082SCtkSGlVOFBwV1JHbFNaSDljNENCMnNMRVZSbzk0QU5hc2Q5bjB2VGVoVWJaZ2p6SVBlUzZWSnZmRzJHNXB1b1I5cEFTeUk4WmdMZVB4aURVNk82ZS9BdUdTWkVUSmc1ckczZDVUNlV1aGY2R3pkOHRzVitOb0lJQmFSVFA2aE5aS3AvQjRCMTd4M1lmcXlPWmxTaXJ4bXFzcXpSbi9PWVZUbTFaTkVFcGIyUVZTWjF3NngxeTd0cUFUbUF4YlY1cU9sWFB1RXJzTXFFbjlGQ1NSbWE3OEV1WDhtamtsWW5RWmxoelBSY2FnT21zYTF4aURma0VuaWJ0dEVxTi9PYWNWcnFsdnZBdnA3M0YzWTVtUXA4M1dUc0tjVXpGeitGZWRFNVAiLCJtYWMiOiJlNTgzYTNmNjNhM2VmMjM4NGQxNjY1MGY5MWI0ZDg2Y2M5MjQzMTE5ZjZmZTllMDg2MjUxYmFmNTE3NzA0ZTVlIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Iks0RHJlMUhVaW43SWRXd0FWL2tWaFE9PSIsInZhbHVlIjoidng3ckpCSXdpT0N3ME0wT2tBM1c1a1gwTFo3Zno3VEoyR0hEUWE4ZHU4ZzNUYTNwbVBtQm9yd1dhajRVWThkZ3l1dWxQYlBDVzgrQzVJdEwrS1dnQ2dKZEZkZnNYcmlsTWdmVUxLSnFkRHR4UDYvZ2xPSGRKckpaTVdMT2RkbVNwUkNydkRFb1lWWnQ3WG1zTVcwSTdIeVJ3Ym5McjhSTzQ1a2crQkVBLzhHc0s3RGVOeW8vZU5VVGFMVVY0RGI4M2ttT05Yd2QyWlZkM0pKSjdPeHQrNHU0T1NGRVMvK2U4UW5hcFRvNS9ZT1Y2R2hWdmo1cUNIR1V1WVJ2U3lIbFVMbVgyd0VFTkRoMEMvVEthUjVWRlUwNE12T3ZoeHVIS3JNWk1meFp2UTJ3dUpJYi9kNWxvSUNYYzU2MllzemFJbksyejV6UTNTTFBHcXJYTkVhc3NDSHlHRzlwUHIxYlQ5UXYvcWNMRk9oOVd0NmJUSzlPVXM0VEtKREluRzl2bkhxSEtzQ0Z3VUtYM24rQ0xra1M1UkRpVmZGc2o0T2F4RlVJZXhnSWdITjl0dlpiWVZ4ZExjdXl4aWMvK3A4ZXV6NnM3aC8wV0N2WmZKMGgwUHNLbEVvR1Q4ZW4rU2ZraVVjZ2VEWjNiTnQ2emhvWTB2dHpqYkNXREE0UW0xTVQiLCJtYWMiOiI3NzVhMzU0OWIzOGZmMGViZDY5YjA5MDM5NzA5MThhY2FhZjM0MjNkNDc1MGRhMmEyNTI4NGZkNjBlYjZjOWM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1808721906\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-475993338 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-475993338\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1630631807 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 10:22:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFGNE80WW5BbDgrblBnZW5yVmI2K1E9PSIsInZhbHVlIjoiVmJ0Y0M2R1dyV3JnMVlBZXdESjY1UVhtM0FEcXMrYUJoSzRoZ0kzSTJWRzdqWWZsT04rSnBGcW43MHNYc0RpMFg4QWl6S2p1bC9abkFZZXV3OUw3dFR4Z2lmdjBXdU5zcE81bUx4ZkNpb1orRWJLYWgrdE9zTGxwWjFQdW05V1BKNitmYmtqdDZYWWoxUSs2UTdITVdOY3V2Mk9DTG0xcFVlSXdYUnF3Q24xWWI4QWowUlF0NElHSUlrRldITDI4UHNHeWtxc0wvMVpzOXFIZk1rWTgyNm00c1ZPYUZ0azhybCtienh6eVg5Q0U1Q21yeUgyTTFDM2xsTmdjMmpIc1RxVzU4THY5RVExQXZlT3pQMjNwbi9Fck5LZndCK01MaU1CVWxvYnNVamhvN25Xc05Pd1paYW96a1l0SE92ZHNCMmgrMy9yTFJlS1owczJpeUVaQ0kvOWhQUWhicWRma3NaWkRkQWcxd21QaGF4MUl2T2I1dlhTL1VmSVlHMnc1dENWUnY0Rk1aMEV5ajlYZDcxaVhqai9iWGxFWFVwNWRYTFk1alZhTFRiNWNpQlhjR0pxRDRDQTArS2lIRDluMEtzTnRkelpiU0NEZ1hIMEhIeHp2K0N3eXBVR1dlQlBzSitsNDk2d3dFNCtESStZd0FOMDBRQVlYTkhYM25weHUiLCJtYWMiOiIzMzI1MTdlM2FiZjJkZjZlMDE4NmMzNjgzN2Y0NWJhOGUxYjg0OWQyNzk1MjIzNWJkOGZlYzhkOWRhZWI0YjI1IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:22:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InExMDAycFFKUVdtVXBkSFhrL1F0NGc9PSIsInZhbHVlIjoiT29JeFpuZzNhM1FPcllacnBrZnkraUhDVmgyTHJiTGVJMDNlbE1VM2FYVlZLR2l6TCs5L1NDRmNkME5sWU5mTGk4S3FkdzNLOUVXS3hlb212SlZmZk91Y2VvcGdsSjQzYUNwdkE5RHhzdXFzSVlVT1JYd3hDSE80YVJ3Y2djR3hEVlJXTDQyd2RmamVjTW9qR3V2L0hYc0YxMTlJb1NJWVhQOWRGTWZtaGd5N0F4OGx2Uk5hcHpkdjFRWXBNbDM4NzJIZzZUNnBoc0JCSG1MUEdvUU4xSmQzUGNQOHF1UitZWWZDRUJWOUE4bXZqTjg4cmFYYmhmOFBBZGUwZ0RvVlVYaHZOS2NYZUU4d0VEM2U1V3IrSGVTblRTWld5M0c0SWptbHU1ckVNMWZBbDR6bHBwajZmQkN2QjJ0NXRDTGF2c3U3Qkd2eTAwdFFkcmdBeUZLZmNoUDRMYnN1cTY1ZHY1amphRDhlSlR2a1R4aHdGL3hvZTQ3bE83V0llOTdsSlZlRkJ2bWJBcHRERlIraHB4bHUrU2YvV2IzUkkyYVlFSUs1T3ZHcVhDR2gvNkF5d09nWVAwcURnM3BMOUtUTklHaHRkYW5lOVlPdGJTS3hVSGdkWXYxUkhLVUExbjFnVnBDd2M4NlpVODVjZzZXYzU3VmtPWWdrRit5Wm9JeTciLCJtYWMiOiI1NWNmNzBkYTA0MzZlMjhmMzdmMzQ2ZjhhMjM3MDgwMWUyZTdmMTc4ZDAxM2EzNDk3MmUyMmVhN2E3N2QzYjE3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:22:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFGNE80WW5BbDgrblBnZW5yVmI2K1E9PSIsInZhbHVlIjoiVmJ0Y0M2R1dyV3JnMVlBZXdESjY1UVhtM0FEcXMrYUJoSzRoZ0kzSTJWRzdqWWZsT04rSnBGcW43MHNYc0RpMFg4QWl6S2p1bC9abkFZZXV3OUw3dFR4Z2lmdjBXdU5zcE81bUx4ZkNpb1orRWJLYWgrdE9zTGxwWjFQdW05V1BKNitmYmtqdDZYWWoxUSs2UTdITVdOY3V2Mk9DTG0xcFVlSXdYUnF3Q24xWWI4QWowUlF0NElHSUlrRldITDI4UHNHeWtxc0wvMVpzOXFIZk1rWTgyNm00c1ZPYUZ0azhybCtienh6eVg5Q0U1Q21yeUgyTTFDM2xsTmdjMmpIc1RxVzU4THY5RVExQXZlT3pQMjNwbi9Fck5LZndCK01MaU1CVWxvYnNVamhvN25Xc05Pd1paYW96a1l0SE92ZHNCMmgrMy9yTFJlS1owczJpeUVaQ0kvOWhQUWhicWRma3NaWkRkQWcxd21QaGF4MUl2T2I1dlhTL1VmSVlHMnc1dENWUnY0Rk1aMEV5ajlYZDcxaVhqai9iWGxFWFVwNWRYTFk1alZhTFRiNWNpQlhjR0pxRDRDQTArS2lIRDluMEtzTnRkelpiU0NEZ1hIMEhIeHp2K0N3eXBVR1dlQlBzSitsNDk2d3dFNCtESStZd0FOMDBRQVlYTkhYM25weHUiLCJtYWMiOiIzMzI1MTdlM2FiZjJkZjZlMDE4NmMzNjgzN2Y0NWJhOGUxYjg0OWQyNzk1MjIzNWJkOGZlYzhkOWRhZWI0YjI1IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:22:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InExMDAycFFKUVdtVXBkSFhrL1F0NGc9PSIsInZhbHVlIjoiT29JeFpuZzNhM1FPcllacnBrZnkraUhDVmgyTHJiTGVJMDNlbE1VM2FYVlZLR2l6TCs5L1NDRmNkME5sWU5mTGk4S3FkdzNLOUVXS3hlb212SlZmZk91Y2VvcGdsSjQzYUNwdkE5RHhzdXFzSVlVT1JYd3hDSE80YVJ3Y2djR3hEVlJXTDQyd2RmamVjTW9qR3V2L0hYc0YxMTlJb1NJWVhQOWRGTWZtaGd5N0F4OGx2Uk5hcHpkdjFRWXBNbDM4NzJIZzZUNnBoc0JCSG1MUEdvUU4xSmQzUGNQOHF1UitZWWZDRUJWOUE4bXZqTjg4cmFYYmhmOFBBZGUwZ0RvVlVYaHZOS2NYZUU4d0VEM2U1V3IrSGVTblRTWld5M0c0SWptbHU1ckVNMWZBbDR6bHBwajZmQkN2QjJ0NXRDTGF2c3U3Qkd2eTAwdFFkcmdBeUZLZmNoUDRMYnN1cTY1ZHY1amphRDhlSlR2a1R4aHdGL3hvZTQ3bE83V0llOTdsSlZlRkJ2bWJBcHRERlIraHB4bHUrU2YvV2IzUkkyYVlFSUs1T3ZHcVhDR2gvNkF5d09nWVAwcURnM3BMOUtUTklHaHRkYW5lOVlPdGJTS3hVSGdkWXYxUkhLVUExbjFnVnBDd2M4NlpVODVjZzZXYzU3VmtPWWdrRit5Wm9JeTciLCJtYWMiOiI1NWNmNzBkYTA0MzZlMjhmMzdmMzQ2ZjhhMjM3MDgwMWUyZTdmMTc4ZDAxM2EzNDk3MmUyMmVhN2E3N2QzYjE3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:22:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1630631807\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1368000687 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1368000687\", {\"maxDepth\":0})</script>\n"}}