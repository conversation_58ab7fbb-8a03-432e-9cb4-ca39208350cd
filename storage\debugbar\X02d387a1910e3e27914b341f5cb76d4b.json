{"__meta": {"id": "X02d387a1910e3e27914b341f5cb76d4b", "datetime": "2025-07-30 10:46:34", "utime": **********.542017, "method": "GET", "uri": "/finance/sales/contacts/search?search=pa", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753872393.714745, "end": **********.542042, "duration": 0.8272969722747803, "duration_str": "827ms", "measures": [{"label": "Booting", "start": 1753872393.714745, "relative_start": 0, "end": **********.425736, "relative_end": **********.425736, "duration": 0.7109909057617188, "duration_str": "711ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.425754, "relative_start": 0.7110090255737305, "end": **********.542045, "relative_end": 3.0994415283203125e-06, "duration": 0.11629104614257812, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46721216, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1238\" onclick=\"\">app/Http/Controllers/FinanceController.php:1238-1305</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01916, "accumulated_duration_str": "19.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.490334, "duration": 0.016239999999999997, "duration_str": "16.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 84.76}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.519526, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 84.76, "width_percent": 4.28}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%pa%' or `email` like '%pa%' or `contact` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1254}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.524821, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1254", "source": "app/Http/Controllers/FinanceController.php:1254", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1254", "ajax": false, "filename": "FinanceController.php", "line": "1254"}, "connection": "radhe_same", "start_percent": 89.04, "width_percent": 6.106}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%pa%' or `email` like '%pa%' or `phone` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1278}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5293899, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1278", "source": "app/Http/Controllers/FinanceController.php:1278", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1278", "ajax": false, "filename": "FinanceController.php", "line": "1278"}, "connection": "radhe_same", "start_percent": 95.146, "width_percent": 4.854}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1801180504 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1801180504\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-703227634 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">pa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-703227634\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-700122104 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-700122104\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-136100967 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IkZtZmNYb2pYRGlpcFNPWmZ4Y0owN2c9PSIsInZhbHVlIjoiN3dSdWZqeTJJZDAwRTlBS29XY1hvOStrSGNBWHNPM1Q0WjUwbmVLTXkvaTZlTzMvd2d3WHBGVFNvUlJ1eFd1bncyVG1EUXNOUTNjbHhuRytRL0c4Q1pnYWU3VExreEd4RzljWTJPbTZBMVVMdmNycFFEQm9vdHdFaXdpR1hyVjdraW1tRDVJb0ZzZG5tME4rUC84VnZLNDB2QkUxRU5QWDdqdEVIeThYVUFUbkdQcXdmZ0l2UHlVc3RMMlhOcnE5SG16SlJQRjVWWGxjVDZYbmRoQkdtNSt3WENvYXNTclBRZVkwekFhbUR5RFhsbzYxbURjNEVreW1oUm5JeDQ4aVlRa3F5Z2dGeDRlMHJuMkF5SkxvTFdGSk01TUZvTFBuRGwyWUlNVkFlbytTZFdaWHBJUEZINGZqa2pMNzNLbjdKTm9JaEdMVGtGekFVemdaYkUzYWJIY0pqdVg0bTYvTVFNTGZzenlSaFYzbTRmS1BSN01KT0UyTkNGcC9SN3NxNFhUQ2lZTWlpamRaZzgyalpkSzdJbCtqY1lWV29MTmUyUzRMZjRXdldFMXZxck1YUXFqK0dXNWFwUUVUNmY4d0ovOFBzb2JYRmNnREliS1F5cWk0aXdUdkxzeWhIaWwwYmhha2dCbVJCT3MraURZTFpNaG1ZREI3aHUyWVhMQmoiLCJtYWMiOiJiMjI4Nzk4Zjg4OTdjYjkwNWRmMjU1ZTc5M2M0YTI2MTNkMzhmMWZlMjE0ZjE4OGVjZGU0MjM1OTU2MTc4NjNlIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjhoV25vZ2ZHcy93MDhVREJrVTlRbGc9PSIsInZhbHVlIjoiOUhGTzFQQTl4MTlTTVFhN2NWVWFBUmFQeTcxdkt0VnFmejMzS0kveEd3VWFBb1NLUnpxMi9uODNTbE82TVNYQ1RGQ0ZjM0lQWVdTZTVaVDFjaGNiMWppS2V3d0Q2YVIyUm9PUGM1NHJ0M2Q4cjVhOE9QNTRrWGtYMWJKa0ZZTloyUEI1WnhvZ1Uxbm1yaVFHRklaems2clNoQkU0YTlyWEhOVVM0UW82amtLZWo5OHc2MHF1SElaRmp2OGl1MW9Id0VoVnBWbzVVT2tob3VEcFBPODUrcVBtSXdhKzZReUJhVWJmbS9obUpnQWhKRUNLQStyQ3U3SDB6bGVUdEVnT1p1anZXa0krOTQxZFVZRjBLWXAybUtYODhMeGdQZ3I2cDNKUUFxa1d5WjUrYlB5SmpZU3VwNlRaUFYvQmM5aWhUQzBRSEIzT3d1aXVaZ1czcGtnTmtqcWZkVnB3bGN2YTJjdnRrcTh1N2ljOURQZnJCMWRRdVBMNk1uZmVoL3pCT1NYeGRScGs5NzZ4Z28vZDkvaWtSWjJDUzI2aEszaXl6dmFITGE5QmpWMzZsNUNQSSsvKzZXK2Rya2RFb1VqV3M2RVptZ3dJRmJDWXo2T1U3d2s2UnZQeDcwYWduOGtqTDdkbjdMWnZoUml3bFpQNFB0R3p0VllvQmx0RHVobSsiLCJtYWMiOiI3ZjNiYTU2NGU5OWRhMmRmOWJhODE4MmY4MTM2NjdlMmJmMzhlNTE5M2JmMTk2ZDQzMTQ4MjUxNTI0Y2NmYWUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-136100967\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-544527285 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-544527285\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 10:46:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFHSERiSG5KZ2gxSnNBTDdyOWpLK0E9PSIsInZhbHVlIjoiRStRaC83Z3o5NkxrY2tNWWkwM3NtK0dZVHRFN1dvRkg2djgyb1NDOGxtYnRFRG1oZ01vT05ncm5aRXZrdE1PQ29TOXVmdG9aM2o0R0d3dTZhSkN4TWJydis1ZWswcjd1aE04QjlHRFc0ejJJRmpuQ0VZZnA2UXhGbGU5RVB6UUpNQUhoVGU2eEdEbUFTenNqRGlaVUpkRG1KNXZPTkpxZVkvaWJZK0lRa3BpOEQyZ2FzZ2s5YWwrU3RDKyt2dlAvN0NwRUw0N0hTYzVaSElDdS9MTU9lUmZzYk5kWEIreXlESVFSOERFaWxuZCtMWngxZTcyZ1FZWkFqRmtGN3A1cWJqbS9ZUExjV3ovMG5YRDNTaUN6VTZDUS94YU9CWTVnMkwweHdmaEYwOVNvYS9NbTVHUVRYcTQ3VWtBUjRpanhzZm9MdGg2T2VkZXBFcU95SGZObE85RDdQRWxMdWJrNEppY2hjaE1oOGtkbkZWRlZ6cU44Sy9BeitEVWRvU0IvaVJRVDJYOFZ4S3BrdklPaUV2T1dZcGpZKzhNbVRSWlJ6ZUNCS3ZhWElRMG5JL0FCV2pJVmUvcEpBcjF4cEIrV3diYW5SaC9QSFRJVDhkdjBPUVhTR0VxTExQbmxZNFhUUHZ1L0ZHL3VNU2ZWcEZNMC9CN2N4dytoRFFOb09oR20iLCJtYWMiOiJkODI4NWNiYTI3Njc5NjcwNDIzM2Q2ZmNlNjMyOWZmMzdmMmU3ZjllNGNjYjE1M2RkNTQ5MmE2NTJiNTZkMmNhIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:46:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjY2b3pYc3ZSbVhIV0hvcDI4WVBXMkE9PSIsInZhbHVlIjoibmdWUTZGRWJFWW1McUdiQzFFQjM0bVhBQ2ptWVpZU3R4SGFZSnAyWUhTdWJqTSsxenlON1ZMSkN4UVpTMDdtTmxNczVCbVBiQStkQVoyYjBiRmdRZWRXcXJFMjE0ZllieVJmTWZ0cW0waldrTllSTithZnRGTnVOR1QveE9hUi9xUEh1cm1SYmh3THdRZkZNakFGcFd1bFhVK1NWVGlYNGZvTXp5RHlCeWRjZUlFQXlmdDRRK3hpTUdjTGdLNk1NQ0pYM2NFcFZIZ0xoT1gra0JZaFJ1ZytQTkNwc09iL2E4QnF0UGVhSk5vckU1aXU1dFhDQ1l5Sy9iTDNWaFFrVzA4dDZqZGoraElqMVcvL3pHbHBhWStSR2k3U1c3akJ2ZmlQY2htQzNFUGRZdUxkSVYzbVpMM3JXeEt6bzJHaVA1QVE4NHBJenFSV1NWbHIvcmdHdlpjWjhUa0FpeDlLYkRIRXkzcEMwN2M1UzMzYXUxQ3BLbERzVUQ0R29XU3FReGFIa3RidWs3b3hTNmFZeEhWNFRaWjI3c2tzSWRWVEtUTnFMNkR0dmIvV204cmRZVGdPS1luVGtQNTdjZTFKWmRWUWdLSWoySUtkWTdzbFUrV1NkcHpyc25EbkxsRHBjcFFRb0kxUEFnTytscVhqVzNid3dpakVzb09QaWg0dEkiLCJtYWMiOiJhNDI2MjRlMTM3MDk1MTIwM2NmYzZmYTQyMWQyMjQ0MTQ5NzcyOWIzM2NkOTk3YzlmNmZiYjE3ZDk0N2QzZWQwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:46:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFHSERiSG5KZ2gxSnNBTDdyOWpLK0E9PSIsInZhbHVlIjoiRStRaC83Z3o5NkxrY2tNWWkwM3NtK0dZVHRFN1dvRkg2djgyb1NDOGxtYnRFRG1oZ01vT05ncm5aRXZrdE1PQ29TOXVmdG9aM2o0R0d3dTZhSkN4TWJydis1ZWswcjd1aE04QjlHRFc0ejJJRmpuQ0VZZnA2UXhGbGU5RVB6UUpNQUhoVGU2eEdEbUFTenNqRGlaVUpkRG1KNXZPTkpxZVkvaWJZK0lRa3BpOEQyZ2FzZ2s5YWwrU3RDKyt2dlAvN0NwRUw0N0hTYzVaSElDdS9MTU9lUmZzYk5kWEIreXlESVFSOERFaWxuZCtMWngxZTcyZ1FZWkFqRmtGN3A1cWJqbS9ZUExjV3ovMG5YRDNTaUN6VTZDUS94YU9CWTVnMkwweHdmaEYwOVNvYS9NbTVHUVRYcTQ3VWtBUjRpanhzZm9MdGg2T2VkZXBFcU95SGZObE85RDdQRWxMdWJrNEppY2hjaE1oOGtkbkZWRlZ6cU44Sy9BeitEVWRvU0IvaVJRVDJYOFZ4S3BrdklPaUV2T1dZcGpZKzhNbVRSWlJ6ZUNCS3ZhWElRMG5JL0FCV2pJVmUvcEpBcjF4cEIrV3diYW5SaC9QSFRJVDhkdjBPUVhTR0VxTExQbmxZNFhUUHZ1L0ZHL3VNU2ZWcEZNMC9CN2N4dytoRFFOb09oR20iLCJtYWMiOiJkODI4NWNiYTI3Njc5NjcwNDIzM2Q2ZmNlNjMyOWZmMzdmMmU3ZjllNGNjYjE1M2RkNTQ5MmE2NTJiNTZkMmNhIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:46:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjY2b3pYc3ZSbVhIV0hvcDI4WVBXMkE9PSIsInZhbHVlIjoibmdWUTZGRWJFWW1McUdiQzFFQjM0bVhBQ2ptWVpZU3R4SGFZSnAyWUhTdWJqTSsxenlON1ZMSkN4UVpTMDdtTmxNczVCbVBiQStkQVoyYjBiRmdRZWRXcXJFMjE0ZllieVJmTWZ0cW0waldrTllSTithZnRGTnVOR1QveE9hUi9xUEh1cm1SYmh3THdRZkZNakFGcFd1bFhVK1NWVGlYNGZvTXp5RHlCeWRjZUlFQXlmdDRRK3hpTUdjTGdLNk1NQ0pYM2NFcFZIZ0xoT1gra0JZaFJ1ZytQTkNwc09iL2E4QnF0UGVhSk5vckU1aXU1dFhDQ1l5Sy9iTDNWaFFrVzA4dDZqZGoraElqMVcvL3pHbHBhWStSR2k3U1c3akJ2ZmlQY2htQzNFUGRZdUxkSVYzbVpMM3JXeEt6bzJHaVA1QVE4NHBJenFSV1NWbHIvcmdHdlpjWjhUa0FpeDlLYkRIRXkzcEMwN2M1UzMzYXUxQ3BLbERzVUQ0R29XU3FReGFIa3RidWs3b3hTNmFZeEhWNFRaWjI3c2tzSWRWVEtUTnFMNkR0dmIvV204cmRZVGdPS1luVGtQNTdjZTFKWmRWUWdLSWoySUtkWTdzbFUrV1NkcHpyc25EbkxsRHBjcFFRb0kxUEFnTytscVhqVzNid3dpakVzb09QaWg0dEkiLCJtYWMiOiJhNDI2MjRlMTM3MDk1MTIwM2NmYzZmYTQyMWQyMjQ0MTQ5NzcyOWIzM2NkOTk3YzlmNmZiYjE3ZDk0N2QzZWQwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:46:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}