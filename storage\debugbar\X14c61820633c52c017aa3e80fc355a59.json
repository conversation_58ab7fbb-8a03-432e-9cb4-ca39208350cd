{"__meta": {"id": "X14c61820633c52c017aa3e80fc355a59", "datetime": "2025-07-30 10:19:11", "utime": **********.198677, "method": "GET", "uri": "/finance/sales/contacts/search?search=ja", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753870750.35967, "end": **********.198719, "duration": 0.8390491008758545, "duration_str": "839ms", "measures": [{"label": "Booting", "start": 1753870750.35967, "relative_start": 0, "end": **********.054247, "relative_end": **********.054247, "duration": 0.6945769786834717, "duration_str": "695ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.054279, "relative_start": 0.6946091651916504, "end": **********.198723, "relative_end": 4.0531158447265625e-06, "duration": 0.14444398880004883, "duration_str": "144ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46721216, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1238\" onclick=\"\">app/Http/Controllers/FinanceController.php:1238-1305</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.03261, "accumulated_duration_str": "32.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.126449, "duration": 0.02632, "duration_str": "26.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 80.711}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.16851, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 80.711, "width_percent": 2.821}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%ja%' or `email` like '%ja%' or `contact` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1254}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.174799, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1254", "source": "app/Http/Controllers/FinanceController.php:1254", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1254", "ajax": false, "filename": "FinanceController.php", "line": "1254"}, "connection": "radhe_same", "start_percent": 83.533, "width_percent": 2.607}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%ja%' or `email` like '%ja%' or `phone` like '%ja%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%ja%", "%ja%", "%ja%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1278}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.18036, "duration": 0.00452, "duration_str": "4.52ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1278", "source": "app/Http/Controllers/FinanceController.php:1278", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1278", "ajax": false, "filename": "FinanceController.php", "line": "1278"}, "connection": "radhe_same", "start_percent": 86.139, "width_percent": 13.861}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-263509382 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-263509382\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-572168629 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ja</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-572168629\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-19006016 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-19006016\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1548801219 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImUvTm8xV29uU2lpK294Y1hUMTRPTmc9PSIsInZhbHVlIjoiRHVDRi94QThSNnZrNlZWY2o2eDFhNUViWGQyOHdiSVl6T3NjOWEybWFMdG00c1ZXV1BYNlViVVlPSGJjUkFJek5UY0F3bWFqU0VWeTBXME9vUHN0dXo2endadkkxWlRqbjZZeGV6eDFJUHhPaFJoWnpQbXRiTFhyWTNPMmIxSnFhUmE2anJzRS9QYVVuekpMSWZ1S0U5Z3pUWUhNRi9nVWg0SStadnUydkExR3NFdjhoVi9tcWc3NFE4NmhRS3BNbHliN2JJam9lSmVxdkF3Y0RBTFNmbVRlSlo0eUErQytpNS9EZmE4bFFGaHhIbVhaZWlCbVJjTFJmeXNKdW1JSGdGd3cxU1dxZCt2bW5Ta0g3MTNnOXNnYjBQLy90THJyUmdsUFpWaVZtS1Q0VHpZWFpNWTY1WTBLTmZBeVlISGFRbEk3eFZPUkcwWnk0TDYxUmozdTh1cEg3MDFDT0RNd1FsL2t0VklGL2psUjRyNHRvMFFuNHlvcWxYVlJqbnFBV25nTzFTb05tVFdRTUREMjhFdHpScUVVTWtnYUgxdFVmdmZsVFRtRHQ2bVlUOVZqWk54cG9TdXJjTVQyOWo5ai90ZUVxdE9pdm1ydXB2MFhRYWhTcy9LV0dsblhKT3lRYjJzTUpqUzNNeTU5cGkzSDJJTXpIckFRRkpVMWErUFIiLCJtYWMiOiJiMjEwYWJkOTNjYmY5N2Y2M2M5MWIxZWQ4NjhlM2E0ODUwYzI4YzJmMjgyOTc1MDg1N2NkZGU5ZTQ4YWU0ODRkIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InFRSVVsMUpONnJQd2MxR0EvZmFmdEE9PSIsInZhbHVlIjoiK1Erbit2aFErVldPR3RzYUpKYy9KSHVnWTZhYmJlSUkyUFh2TUM3cittaTJnM1hyS0dPZ2gvMkQ2Zlp1eDhwR1l4RWg5Y0xvUElnK1Y0SFR3dkM5QWlUL2pUS2krUkplWktxcGU3V0lYaEJibFpwMU45TERNK1NoTzRGbjB6M1R5Sm5pSU1rS0w2MnRGZUVVcEtVdEN3cjR3SmFpU2hTcnd3MUl5aERjT3JUYzU5aEpzaGVSam5PYWFFbkc3UTR1L3crQ3BFSWlKMEFEQnJKL0F1SkZDTGpEOVQ4U29EcmZnQ0grcEtsa0ptaVlRTG41WFMzS2wyVHAwOHZNdUVxRjdnbVNBaDl5ajBKWmdaNXE5MHNHeElRaTFSUDdOMEFOUW1rd3UvSWplUDRzZXNqY0RiU3hDUzZ0VzVvRG1GUkhzcmU5MHB4bVozWHlob0dHbEMycGh0N05LWVl3TkYvVS9SS0lDb1IySjd5UVV0ZG5wcmhFN2daWDNzYThoK2lCWngrcDhjZlcyZURIUkwxNjBaSVlCSFd2bWhvditIZXZ2M09sTytlTTRMT2ZlNEZVMUlNZTVNczBiMzNwcDlJYjMvS3BZcTF0eWJJckl0TU5ianhQRTZqZFp0RmFOaDNUSnNtOHFyRGVYVWd5YWJ4YmNrdC90TU80S1UwUVRqaUQiLCJtYWMiOiIzNDNhN2M5NWQ1YTUxMWM1MzBhYjk3OGVhYjU0ZTZiMTAzYTY3NmUyOGQwYjEyNmU2MTc3ZTdhYWE4ZDc3ZjQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1548801219\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1224566837 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1224566837\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-424762940 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 10:19:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InE3SGdLbTNUNnpIWWRLRmZHNE1GRGc9PSIsInZhbHVlIjoieU1vMWZYalJhUHJUZVVVT3Fhb0d2b3B6QkRIemtNQzZ2QUozVVBsWTFpMzJkaDlJZE43d3IzcnVTTDk2UjFBcGdFVGx5aDZzTS9lVE5tbUk1V210aDFveUhuZ2hlRkZ6VFcydTExTTlWMTl3QU85WXVOY1FLZFJmeFN4K2Q1UlFHSWNHUGdFWlVhc3UycHorRlV0VXY3THcrRitQcGtqbHo0NnZGS3hTQUtzMEhWaTZJTnFGb0dkbXYwRUg4ZlJCVk1DZW9sK2VRY3JrbGRaLy9zQzBPbDJUUWRyUFhkZVVTYWViZVVkaDZic2lWMG5BK2J5ZXVmaTBJcTJtcHArVUU4dG9oRjlSbnlaZXBDRTFxZnpxNk1xMGlzcHBwcjM4emdsQ01NRS9jTTI1OE1vMVd2bXdJRU9DWnJ3Y25rYmlTNXVaTkxMbkMzM1F3dXpEVmFGZEJaVnhjc0VUUU5HTVZhc2xLeEFFa3NTelpxWUdsUHgzaDZtZDdIMlgzdTN6dG5yKzg1bjhjUE5GdmxKUTJJQ0MwblIwN2pYNm12aWhUQThTQi9QVFJsU1l4UzRjbTVxWmNCMG5uTkM1VldISUlNL3V2TkYzbndrNEdSd2RqSEFtUG5zM0laRERDK3NzaExwNGhXSTY5cXhRQ2ZoOWcwbDU3WFlsQU9tR213ZDUiLCJtYWMiOiIzNmFhYTViMjNmZDZjNDMwMzllYzRmNzQ1Yzg0ZThiNzFhZDgyOThkMDVmNjBhM2I2ODRlMDgwYjI1NzRlMTUxIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:19:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InNYSFFNalNKUFZEQTRsdzAyZVZQdlE9PSIsInZhbHVlIjoibm5DeGdBOEIwYm1EN2NicDVyaEpDR1hzT0daY2hkczZUQUFiQjlmQnNjSE52MVc3TllkNEJkMzFQRzNJY2dQckg5RldmMDlFZ2EzdkdHNkgwQUVFOGwrR1NGNmN3UTZSVkNFSjFFQXFJRU1MNEszd3czVzdxVDJlZUZsYjRDeFlRT0lmc254aXk5cFdHbFRUVUNLQnprYjdmSEMvZjd0VDNVUVYvMVNRTXlFZXgzRzhvWHR3alpQMkp6ZHF6ek9tM2s2UkFKeFc1ak04Uk8zMTF5NmsxZEJjQ3J4MitELytQOUpsQ1M5N05TU2tYRGhCWkRiNjFCM0Q3SnVNYlNVa2p1K2xEeUNhZSt3QlM0VHRLdFRrZGd6cjRvNDdYTWtjTmx1UVdkL0FEYUZPR0xZTWJ3c3VpZWNkQ2JZVHZPcG94VjVTanRJZVJsNmNoL0d5VFV1bDFiNzlLRncybThEdnVrRzNHRDBVUFNXdmV4T0l4bG90eWlrTFRLQVRDRUp2OXRYWFZXckxHeGVzZzFaRHNrcENPVzVIMzcxQloxRHFocVk5dW1pZWpwM0xQbzFBM3A5dnJQWVArYkcyblQ3eDZWY2FQSUxKVXd0RDBnU3hOYmJoS1RGNE9sUEtTMG1vdkRuT1ZKUng5SE50TUtJWFMwZmY5RC9MZTJ5SnkwZWwiLCJtYWMiOiIxNzY1M2I1MjA2Mzg0ZTFlMjk4ZTA1NjFkMTUzNTQxZTg2NWVkZGNlODAzNTE2YTljZmUwZWY4NGQ0MmJlYjlmIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:19:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InE3SGdLbTNUNnpIWWRLRmZHNE1GRGc9PSIsInZhbHVlIjoieU1vMWZYalJhUHJUZVVVT3Fhb0d2b3B6QkRIemtNQzZ2QUozVVBsWTFpMzJkaDlJZE43d3IzcnVTTDk2UjFBcGdFVGx5aDZzTS9lVE5tbUk1V210aDFveUhuZ2hlRkZ6VFcydTExTTlWMTl3QU85WXVOY1FLZFJmeFN4K2Q1UlFHSWNHUGdFWlVhc3UycHorRlV0VXY3THcrRitQcGtqbHo0NnZGS3hTQUtzMEhWaTZJTnFGb0dkbXYwRUg4ZlJCVk1DZW9sK2VRY3JrbGRaLy9zQzBPbDJUUWRyUFhkZVVTYWViZVVkaDZic2lWMG5BK2J5ZXVmaTBJcTJtcHArVUU4dG9oRjlSbnlaZXBDRTFxZnpxNk1xMGlzcHBwcjM4emdsQ01NRS9jTTI1OE1vMVd2bXdJRU9DWnJ3Y25rYmlTNXVaTkxMbkMzM1F3dXpEVmFGZEJaVnhjc0VUUU5HTVZhc2xLeEFFa3NTelpxWUdsUHgzaDZtZDdIMlgzdTN6dG5yKzg1bjhjUE5GdmxKUTJJQ0MwblIwN2pYNm12aWhUQThTQi9QVFJsU1l4UzRjbTVxWmNCMG5uTkM1VldISUlNL3V2TkYzbndrNEdSd2RqSEFtUG5zM0laRERDK3NzaExwNGhXSTY5cXhRQ2ZoOWcwbDU3WFlsQU9tR213ZDUiLCJtYWMiOiIzNmFhYTViMjNmZDZjNDMwMzllYzRmNzQ1Yzg0ZThiNzFhZDgyOThkMDVmNjBhM2I2ODRlMDgwYjI1NzRlMTUxIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:19:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InNYSFFNalNKUFZEQTRsdzAyZVZQdlE9PSIsInZhbHVlIjoibm5DeGdBOEIwYm1EN2NicDVyaEpDR1hzT0daY2hkczZUQUFiQjlmQnNjSE52MVc3TllkNEJkMzFQRzNJY2dQckg5RldmMDlFZ2EzdkdHNkgwQUVFOGwrR1NGNmN3UTZSVkNFSjFFQXFJRU1MNEszd3czVzdxVDJlZUZsYjRDeFlRT0lmc254aXk5cFdHbFRUVUNLQnprYjdmSEMvZjd0VDNVUVYvMVNRTXlFZXgzRzhvWHR3alpQMkp6ZHF6ek9tM2s2UkFKeFc1ak04Uk8zMTF5NmsxZEJjQ3J4MitELytQOUpsQ1M5N05TU2tYRGhCWkRiNjFCM0Q3SnVNYlNVa2p1K2xEeUNhZSt3QlM0VHRLdFRrZGd6cjRvNDdYTWtjTmx1UVdkL0FEYUZPR0xZTWJ3c3VpZWNkQ2JZVHZPcG94VjVTanRJZVJsNmNoL0d5VFV1bDFiNzlLRncybThEdnVrRzNHRDBVUFNXdmV4T0l4bG90eWlrTFRLQVRDRUp2OXRYWFZXckxHeGVzZzFaRHNrcENPVzVIMzcxQloxRHFocVk5dW1pZWpwM0xQbzFBM3A5dnJQWVArYkcyblQ3eDZWY2FQSUxKVXd0RDBnU3hOYmJoS1RGNE9sUEtTMG1vdkRuT1ZKUng5SE50TUtJWFMwZmY5RC9MZTJ5SnkwZWwiLCJtYWMiOiIxNzY1M2I1MjA2Mzg0ZTFlMjk4ZTA1NjFkMTUzNTQxZTg2NWVkZGNlODAzNTE2YTljZmUwZWY4NGQ0MmJlYjlmIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:19:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-424762940\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-9860536 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-9860536\", {\"maxDepth\":0})</script>\n"}}