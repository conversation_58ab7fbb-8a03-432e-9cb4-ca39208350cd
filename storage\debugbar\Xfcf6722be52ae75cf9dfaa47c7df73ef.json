{"__meta": {"id": "Xfcf6722be52ae75cf9dfaa47c7df73ef", "datetime": "2025-07-30 10:20:45", "utime": **********.906842, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.180177, "end": **********.906899, "duration": 0.726722002029419, "duration_str": "727ms", "measures": [{"label": "Booting", "start": **********.180177, "relative_start": 0, "end": **********.833833, "relative_end": **********.833833, "duration": 0.653656005859375, "duration_str": "654ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.83385, "relative_start": 0.****************, "end": **********.906902, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "73.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3038\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VpZCqRaeTGUtpp7Hx3F3onrgs6rqT3TNUD3BGDbD", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-297530322 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-297530322\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-718583919 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-718583919\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1922005902 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1922005902\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-590050767 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-590050767\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1026895352 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1026895352\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 10:20:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJtWndvTjFEUlhyL3BPL2dqZkFRa0E9PSIsInZhbHVlIjoiRXV0VnA0K05FbVpmbnFvaHp4U2hCWG1SSTBqbWtLMjEzUlVLVDgzRkJUbVpQUW9nMHdEZkFWOXZGTnc3NU4wWkhocjJkWG1mZFBUZVJSbk1wQW5ySk80Z0RuVzJvMlRBU21vWVVGYkEyNkIzR2NORjlUOVBzUG9VZGppNG9mSk50NFVtRm5iVXZlWHVTOHJHQnRaYmRtci9GZ1J3cWVyZUhzNE15T3dRQlIzZ0VqS2ZxTGZ5aTN0ckxmVU9HbndwWTMwZEhwNVVqdGp5ckJ6WVFoL0ZISzBLcnVpV1lQVjkrNkxpWVRIWjdoZ045OEttK2tmMnM5akR3MXRUcmJrZ2dvYldKWFU0N0M2dWRhUjNxRjdWeVJlaFpMTlNpRVJwVzJFRjYrTWtTTDQ0R1RMSVowaDd5WUpwMkF5Z0MwVEc5L2E0WG5EMVQyaVByczRWMW52UUJiSWpaUHR4WitBK2ZKb0VkZU9iZWtjRnY1UXV3dENLU1NRWmZ1UGx1WEtJdlhpWjl2djlEaE4rOGswZiswdHJ2cVBKMVZCYmJSZStXajRXNGh0Y0dLZHZBUElJWnhJWUthSkd6NFdGK2hyaWZSeSt2NkhlQjN2ZXM2UzNVQk5WSTl4MVh5emVJd0VTT2Zpc01lZm1IV05FZ0ZiSHhYOHZ2SGV6ajlRT25CdlgiLCJtYWMiOiJjNDc1YzliYTk1ZTM5MGVkYTBlZGExOWVmZTkyYmI2MDdkZmEzY2ZkM2YwMzc2ZTA2M2JkNjdmYWIwZmY4OWFkIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:20:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlBTREFTVnZwZUtvWUJHZFlzYWduanc9PSIsInZhbHVlIjoiY0Rqd3M5NkM1ZnFSbzF2Rlo5bCttNTVzMUQ4ZG9XTERnSlFGWFhJK01QakFqaEw5NVdycWxCbC9xM0IzYjhUbWJqZHN0WFdwNmFMTm9ISFJoV1ZMTUU3YkNCYi9UV3JWWTdyVWZROFpnTVMvYlIvY3d5MXVGYjNoUE5UNy8yUENJM3dVWUIrM2lGMXowMzQvaEJlTEdtMU80TENRZFRGb3ZLM1BVc3BUbzNJWWF0UVBBY0N6L2l4WVpaZkZWNEVwN2pmL1dUdDJTbStIVVE0UVFMY1BmM2xFUHRhWDJ3VE1LWDRPSDdtb2dxTTBEZGNVTmhDb25IeFFqYWVxTUhEZW4yMzRRVnFRVmdpYVlIdjF3cWxVMUpLM0ltUFBmVzlKRVhUd1pRTmFvVWM1SkZPRS9DdnRPeWtHcFBsYzZ0YTdKSkx1TlYyVWYxTXNPblowRG5hcVlkaWZjUkVncEg5eVpCZkFNRzlOa2hNZkp6SWRiOXQ0YTcyQjdubzEzMVZaZzMvNFVmL3lSb0M2MWRyUndRUm1EaWNYekJJMlVIaFFveTc4Rm1lVHBSa2M5VDQrdnMzWW5QR3BmS2hGY1dUdHNoTytXT0x1Wk95ZWxGZzAvT21YR0twc0ltemZGWXRGeWxHNDJDZVlPK3o4WFpDME9obitrYVJHbGVtNTFvWUsiLCJtYWMiOiI1M2YyMmEyNDFjOGI5YmQ5OTRlZDkzODNkNmQ3MTcxNTIyZmRmZjljZDhhMDE3MzA4ODZkZjVlN2RhNTExYjQ0IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:20:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJtWndvTjFEUlhyL3BPL2dqZkFRa0E9PSIsInZhbHVlIjoiRXV0VnA0K05FbVpmbnFvaHp4U2hCWG1SSTBqbWtLMjEzUlVLVDgzRkJUbVpQUW9nMHdEZkFWOXZGTnc3NU4wWkhocjJkWG1mZFBUZVJSbk1wQW5ySk80Z0RuVzJvMlRBU21vWVVGYkEyNkIzR2NORjlUOVBzUG9VZGppNG9mSk50NFVtRm5iVXZlWHVTOHJHQnRaYmRtci9GZ1J3cWVyZUhzNE15T3dRQlIzZ0VqS2ZxTGZ5aTN0ckxmVU9HbndwWTMwZEhwNVVqdGp5ckJ6WVFoL0ZISzBLcnVpV1lQVjkrNkxpWVRIWjdoZ045OEttK2tmMnM5akR3MXRUcmJrZ2dvYldKWFU0N0M2dWRhUjNxRjdWeVJlaFpMTlNpRVJwVzJFRjYrTWtTTDQ0R1RMSVowaDd5WUpwMkF5Z0MwVEc5L2E0WG5EMVQyaVByczRWMW52UUJiSWpaUHR4WitBK2ZKb0VkZU9iZWtjRnY1UXV3dENLU1NRWmZ1UGx1WEtJdlhpWjl2djlEaE4rOGswZiswdHJ2cVBKMVZCYmJSZStXajRXNGh0Y0dLZHZBUElJWnhJWUthSkd6NFdGK2hyaWZSeSt2NkhlQjN2ZXM2UzNVQk5WSTl4MVh5emVJd0VTT2Zpc01lZm1IV05FZ0ZiSHhYOHZ2SGV6ajlRT25CdlgiLCJtYWMiOiJjNDc1YzliYTk1ZTM5MGVkYTBlZGExOWVmZTkyYmI2MDdkZmEzY2ZkM2YwMzc2ZTA2M2JkNjdmYWIwZmY4OWFkIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:20:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlBTREFTVnZwZUtvWUJHZFlzYWduanc9PSIsInZhbHVlIjoiY0Rqd3M5NkM1ZnFSbzF2Rlo5bCttNTVzMUQ4ZG9XTERnSlFGWFhJK01QakFqaEw5NVdycWxCbC9xM0IzYjhUbWJqZHN0WFdwNmFMTm9ISFJoV1ZMTUU3YkNCYi9UV3JWWTdyVWZROFpnTVMvYlIvY3d5MXVGYjNoUE5UNy8yUENJM3dVWUIrM2lGMXowMzQvaEJlTEdtMU80TENRZFRGb3ZLM1BVc3BUbzNJWWF0UVBBY0N6L2l4WVpaZkZWNEVwN2pmL1dUdDJTbStIVVE0UVFMY1BmM2xFUHRhWDJ3VE1LWDRPSDdtb2dxTTBEZGNVTmhDb25IeFFqYWVxTUhEZW4yMzRRVnFRVmdpYVlIdjF3cWxVMUpLM0ltUFBmVzlKRVhUd1pRTmFvVWM1SkZPRS9DdnRPeWtHcFBsYzZ0YTdKSkx1TlYyVWYxTXNPblowRG5hcVlkaWZjUkVncEg5eVpCZkFNRzlOa2hNZkp6SWRiOXQ0YTcyQjdubzEzMVZaZzMvNFVmL3lSb0M2MWRyUndRUm1EaWNYekJJMlVIaFFveTc4Rm1lVHBSa2M5VDQrdnMzWW5QR3BmS2hGY1dUdHNoTytXT0x1Wk95ZWxGZzAvT21YR0twc0ltemZGWXRGeWxHNDJDZVlPK3o4WFpDME9obitrYVJHbGVtNTFvWUsiLCJtYWMiOiI1M2YyMmEyNDFjOGI5YmQ5OTRlZDkzODNkNmQ3MTcxNTIyZmRmZjljZDhhMDE3MzA4ODZkZjVlN2RhNTExYjQ0IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:20:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2046351583 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VpZCqRaeTGUtpp7Hx3F3onrgs6rqT3TNUD3BGDbD</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2046351583\", {\"maxDepth\":0})</script>\n"}}