{"__meta": {"id": "X9f243418f2e1cc264c645aa703ee9c8a", "datetime": "2025-07-30 10:20:59", "utime": **********.904248, "method": "GET", "uri": "/finance/sales/contacts/search?search=pa", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.06964, "end": **********.904269, "duration": 0.8346290588378906, "duration_str": "835ms", "measures": [{"label": "Booting", "start": **********.06964, "relative_start": 0, "end": **********.766401, "relative_end": **********.766401, "duration": 0.6967611312866211, "duration_str": "697ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.766415, "relative_start": 0.6967751979827881, "end": **********.904272, "relative_end": 3.0994415283203125e-06, "duration": 0.13785696029663086, "duration_str": "138ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46726224, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1238\" onclick=\"\">app/Http/Controllers/FinanceController.php:1238-1305</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00898, "accumulated_duration_str": "8.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8540728, "duration": 0.006030000000000001, "duration_str": "6.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 67.149}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.880527, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 67.149, "width_percent": 13.029}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%pa%' or `email` like '%pa%' or `contact` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1254}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.886848, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1254", "source": "app/Http/Controllers/FinanceController.php:1254", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1254", "ajax": false, "filename": "FinanceController.php", "line": "1254"}, "connection": "radhe_same", "start_percent": 80.178, "width_percent": 9.911}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%pa%' or `email` like '%pa%' or `phone` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1278}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.891105, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1278", "source": "app/Http/Controllers/FinanceController.php:1278", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1278", "ajax": false, "filename": "FinanceController.php", "line": "1278"}, "connection": "radhe_same", "start_percent": 90.089, "width_percent": 9.911}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-76949485 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-76949485\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2093949486 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">pa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2093949486\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-436836683 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-436836683\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2126310888 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IlFMc0drTUlLa3hDOUNtUFkrUzFJbkE9PSIsInZhbHVlIjoianpPVEtZcTc5ZUZ6Y1FOdFFiNUFSYW5Td3FNNXJDMXNTVFNnUmw4bktadWlvcWdEQnZGMm5qbzNySDVUNmwvZkVOZnFlblAwQTZmNWNYWlZPbHFHdkZOU3VOY1E2Y29aSlBYZmIrK2krNXVJM3hsM2cxRTIwUEsrbkFvOG1xSmsrcFB5RWdFaUhFKy9sZm8yd0FYdTRTai9HTlJoV0tZRXdOZjR4czBDTFZPNzkwSTZ5NCt6clVmV0JHYXpreUgrR0FkcElaRkRWTHVLYmo5c3h3UWNvWCtHaVRSODBRaVkwcEJDUFo3KzVtKzNoWVAxN1djR0czRy8yMlEvcUZBRzhCMFhyYURQWjNRbEwxRjcxQ09jZzNWd0QzbEdRc1VWWjB2SWpETzA0Rlk1YThwcmZteHZoZHpNMnpLRXVsRDFyd3c0RysrMTFISzJQN0VnNGdPZDlLVllOMFB2eEk5VVNxRVFsWnNhc2U0bGMrZUZjaEtKTVdUYU00blF0cHdBVmtNSHJpN0N1cGIxN3hzSWd1QXV5L1hwdk4yRi9COUYwcER3RkREaUIyUWY1aERsZjFvSkRyb29Yb2txSFBPQUVDTzlwT2J1WlVZVTUvenRXY1F5VFp6end2U01BUHpYQjk1SjFmOG00S0lLenpJcTl2WHI1a056K0ltYW9XV0kiLCJtYWMiOiIxZTI2NDUxNGYyZjQ3ZDhmMjNjYzYwMjI4OWI4YzRlNDBhOGRmNDA2YWYyMjhhNmEwYTgwMTI0ZjcxMTFiZmIzIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InhUTjNKREpBblU0KzgySCtTL2txa2c9PSIsInZhbHVlIjoieVlpV2JQakU0N0R5K2JsdmZqTmgxbFloRVNCeTdsalVBRzJSbHE3MVFNOUR2ODFvZ1NqKzdHalJlOTFEczRNdWExVFI2UFdsTWVJbTJ4elRlNlhVcTJBT09XS3E1UlNpa21jN1dpdnBIZXdqc09reUZJVWVtdmpObzZUZ1V4dU9RZWtxTlV6VmxoNjNKMlRDQkg2SDdnN1h0RE44dWVNNExWeUpRSnNacmRHWUp2TTVIVXFmbkc1alFEVzE0V0tVM1hHRldUN0hkZ0x3YVFaNkxFVGtjVHAwL2g4WVdtYSswVnBDU0p2NnEwSEFFc00vMzEwN3RzTjY0ZTluTzRlT1RGRzJHbGlzc3ZJa1lkcWIwenVUdU1oUmRCeW9JbjFFcjMxcW5UWmRaYUQxM0U0eC82eTVPUVFZS3cvVDRFQjk3clRVS2ZONVZNZ3BEWlVPdzB2bkxCOU41UkdzNzNlVm1MUjlucFU2Ri8xTUJ5cnNuZXVPaWhKMUE5WkQxODIrNjhwVU1nK1J5VnNMNmwwL2R3K2V2bzljL0dCL1ZsSWZHOHpNaURodXNjdXlDeVYyUERnWkFta2R6WWpzTFQzSUJCWkMzWUhvWDBFQ0cyVExpUWNPS0Z1R0FJZzZvZytlSzRGc3I4S2ZVV2RudVBLYzM5dDRnM2tzSERCc1lsbzMiLCJtYWMiOiIxNjMxMzYzNTlmM2MzMTJlOTFjOWFiYzFiMTNjNDk5NGIyYzZkNGMwYzg2NjNhNWVjYjFmMDgyZGM0YzNlOGMwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2126310888\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1169117029 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1169117029\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1727908067 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 10:20:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5ZS3ZZajlOdFBVeU0xTENHd0F0eEE9PSIsInZhbHVlIjoiejdQV1NKRGNScHRudHFsTEV4amI1YzFOU29zTUFheUdZSlFXL3RCRGVoUklUZXliQzJSL2VodDV0QmRnQnJGS3ZIUTFXV2NtcW13dG05OWhJWlVNcTZ3OHFtdWJLZEtveDNKM21Lb2ZoMlBhYVlrazBKUXY1eG94akxGaWpiYW1BM3VXcENLZHVpWjZ2MjF1bWpITmVuODRTY3BSWExOOGhwbXRDUnUwOVNUdzZjM0JJQUlROTZ4U0dsNlM3cWVLOGRFTHJFdjR1YzlGYU40RTdUZXZoT3dneU5qSy9XRjNnYkN2bWEyS2NPYm92RnlwUWFENlc3T0FHVncwcjZvRVdhVUZ1RVZpLzdOUHVFZkZ2ZEJaOE04Ukhyb1p3dzBJS3YrQW1YNEl2Slhmd2ZOanlSdlpMMzRJWW9oOVExRjJXYzRxZ3MzeVJPOTdJcXZlRGhPUXk0NytXclFiUm1nT0xIZkg4cGJJQ01rODMvQ1VPNGVZN1lGSzRlallCazk5OS9aeThvUEhNbGpndGEyeHlubHMyZEpRVEQyaUM5RUNta3R4VFE5QWUwblM0V09FZXREMXgwVFFIRG1OTG5TajZ6TGlGSm9TN1plZnliOWIvQ2U4UCtLK3F5bkhaS3R4MHVNdyt2ZVdtNEVuSXNsU2oxM3IvTlZaUHE0bmdXL1ciLCJtYWMiOiI0ZjI1MmE5N2NlZjA5NGNhZTM0MjljNTU2M2UzZWFiY2ZmMTEzNTc5ODJmMGY1ZTYyOTYwMWIxZDQ4MjkwNmZmIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:20:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IklxUlRTNUlWU2c1S0xiM2djaUlIZnc9PSIsInZhbHVlIjoiakxXTFA3b28rTk8xT2V0aVVzdjYwbjhNWXdUcDlud2g1djRJRUYrWFZXU3dyZ01MT083WER2NndXOUdsMGFna3k4WnJsNm5vOGJnWDhGOEcvVnBqR2dUSXhQRThHZGtLcmdjdk1raUZlUjRtV1pMSTgwQmhrenkwdzVIYWt4QXJ0amxScG0rYXZmT1dIUXBmb01wMURnVkxVK0FjMDZNQ2QwVHM5N3l4aElkaUxBN0FrOHZjTGxlZjRqVzA1aTNTR0dPd1R0R0h4U21sdkdsSGM4UkhKQmpCWXEyWTBTcEN4OTB5VlpFRWFwcWR6NS94S3lvaDZwU3pqZHdIZzJnVHpZT3RadnoxQ2EwenRqTmRWdnc5bHVnZGI5bzR4b0MySlN1SzBoblBzSUY5L010NDFsRFJXVFNqTWtWSFNIRGhUK3pXM1VtcUJ4WjhoSmg3RDhMOVIxL2NodzRDVjFNWUJoY0JLK3k3S1E2T3dnOXV1KzFXUXk1U25YcDF4ZkxRdHJDa2lqRm8xUjNiUU9XRFpjMEpZSnJBVjY4TFNBMnhudmRwYWVVaUllS0dmSmtPZUF3Z051RVFISzRlS3BSRE4xbE5zd1N5eERQbzY3Uis2Nk4wSStpM1ZsUDVOTWh0SE5aOXhSb1I0NXBpRHNMbDNjUXdlaVdRTUE3Sm56S3EiLCJtYWMiOiJjN2M5ZGVlZmE3NGM2NWM4ZDA5MTc2NTAyMjAzYWIzZmFjMDk4ZTgxMzgyNjE1OTdkZjI3YWMzOTgzZDYxYmU5IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:20:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5ZS3ZZajlOdFBVeU0xTENHd0F0eEE9PSIsInZhbHVlIjoiejdQV1NKRGNScHRudHFsTEV4amI1YzFOU29zTUFheUdZSlFXL3RCRGVoUklUZXliQzJSL2VodDV0QmRnQnJGS3ZIUTFXV2NtcW13dG05OWhJWlVNcTZ3OHFtdWJLZEtveDNKM21Lb2ZoMlBhYVlrazBKUXY1eG94akxGaWpiYW1BM3VXcENLZHVpWjZ2MjF1bWpITmVuODRTY3BSWExOOGhwbXRDUnUwOVNUdzZjM0JJQUlROTZ4U0dsNlM3cWVLOGRFTHJFdjR1YzlGYU40RTdUZXZoT3dneU5qSy9XRjNnYkN2bWEyS2NPYm92RnlwUWFENlc3T0FHVncwcjZvRVdhVUZ1RVZpLzdOUHVFZkZ2ZEJaOE04Ukhyb1p3dzBJS3YrQW1YNEl2Slhmd2ZOanlSdlpMMzRJWW9oOVExRjJXYzRxZ3MzeVJPOTdJcXZlRGhPUXk0NytXclFiUm1nT0xIZkg4cGJJQ01rODMvQ1VPNGVZN1lGSzRlallCazk5OS9aeThvUEhNbGpndGEyeHlubHMyZEpRVEQyaUM5RUNta3R4VFE5QWUwblM0V09FZXREMXgwVFFIRG1OTG5TajZ6TGlGSm9TN1plZnliOWIvQ2U4UCtLK3F5bkhaS3R4MHVNdyt2ZVdtNEVuSXNsU2oxM3IvTlZaUHE0bmdXL1ciLCJtYWMiOiI0ZjI1MmE5N2NlZjA5NGNhZTM0MjljNTU2M2UzZWFiY2ZmMTEzNTc5ODJmMGY1ZTYyOTYwMWIxZDQ4MjkwNmZmIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:20:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IklxUlRTNUlWU2c1S0xiM2djaUlIZnc9PSIsInZhbHVlIjoiakxXTFA3b28rTk8xT2V0aVVzdjYwbjhNWXdUcDlud2g1djRJRUYrWFZXU3dyZ01MT083WER2NndXOUdsMGFna3k4WnJsNm5vOGJnWDhGOEcvVnBqR2dUSXhQRThHZGtLcmdjdk1raUZlUjRtV1pMSTgwQmhrenkwdzVIYWt4QXJ0amxScG0rYXZmT1dIUXBmb01wMURnVkxVK0FjMDZNQ2QwVHM5N3l4aElkaUxBN0FrOHZjTGxlZjRqVzA1aTNTR0dPd1R0R0h4U21sdkdsSGM4UkhKQmpCWXEyWTBTcEN4OTB5VlpFRWFwcWR6NS94S3lvaDZwU3pqZHdIZzJnVHpZT3RadnoxQ2EwenRqTmRWdnc5bHVnZGI5bzR4b0MySlN1SzBoblBzSUY5L010NDFsRFJXVFNqTWtWSFNIRGhUK3pXM1VtcUJ4WjhoSmg3RDhMOVIxL2NodzRDVjFNWUJoY0JLK3k3S1E2T3dnOXV1KzFXUXk1U25YcDF4ZkxRdHJDa2lqRm8xUjNiUU9XRFpjMEpZSnJBVjY4TFNBMnhudmRwYWVVaUllS0dmSmtPZUF3Z051RVFISzRlS3BSRE4xbE5zd1N5eERQbzY3Uis2Nk4wSStpM1ZsUDVOTWh0SE5aOXhSb1I0NXBpRHNMbDNjUXdlaVdRTUE3Sm56S3EiLCJtYWMiOiJjN2M5ZGVlZmE3NGM2NWM4ZDA5MTc2NTAyMjAzYWIzZmFjMDk4ZTgxMzgyNjE1OTdkZjI3YWMzOTgzZDYxYmU5IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:20:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1727908067\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1524461768 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1524461768\", {\"maxDepth\":0})</script>\n"}}