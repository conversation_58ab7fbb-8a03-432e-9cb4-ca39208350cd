{"__meta": {"id": "Xadfe6b9bd3d2d79ee77be444ba50d742", "datetime": "2025-07-30 10:43:49", "utime": **********.962261, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.011087, "end": **********.962294, "duration": 0.951207160949707, "duration_str": "951ms", "measures": [{"label": "Booting", "start": **********.011087, "relative_start": 0, "end": **********.841365, "relative_end": **********.841365, "duration": 0.8302781581878662, "duration_str": "830ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.841401, "relative_start": 0.****************, "end": **********.962297, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "121ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3038\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qIxGkzrR170Y3S3ZV9TiQSn8i4NIauirZ2hSxOQ6", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-760451799 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-760451799\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-736416027 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-736416027\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-82228261 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-82228261\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-338662078 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-338662078\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2005476369 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2005476369\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-188358912 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 10:43:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVpMHlvakpZSUMxYnZmMjY5STY0ZXc9PSIsInZhbHVlIjoiK1VQSEpMYXRpVlQyeEsrYlBaVVp4TWhlTFZhdFdCTVFhL0NwU0xhK1UrK0NxdFFGRGEzQ2REODUvVmhtNEd3QlBwTzFMLzhLc3VOUzdwNEVvbU9xNzd0SkVRRHYzTGpTbFdheUlXNmJYMENPbEZId25CYWFuM09BNDJhOE4rOGZmM2JydFo5SkNKT29iNEZBOFVVVXRLY01HQkpUS0UxK3prUXFUd096dklMK1g4RnZ0UXBvUW43anZRTUFSZW9NM2hFZnFtSzYyMCswaW9LTm4xc3FySHNYOTlERkFoSGVpYnZXRFU0eElNK0hkc3loZWIrYWdNMThXdFZZZ0t6RVlTT3pDUFlTS2VlUlRYL0NBRDgwQTBoZzRLcGpEaEt0ZmMvOWYxeG9GcFpUNzRuRFBkS2pFUGYzaGZiQ2Rsa3BOL0duempNc1BENERVMnNubEgwTGVQSTNaaW4wVEVkaWJnSmdqNTJ5bFp3SUgwL2Q2dG9CMnFnQllJaC8zaTg2UkJ2M1lCQ3NZZlBGOGNqYlhXMkNuZXhqN3h5aS96eVBPSTRqVi9aK2NJTHlXSGs1WVZNK3BnbDh1cUJ3cFdUclBGL0ZRWnhnQVREc0hkM0R3Wm8xZjdVOXBhcFI5WWkvU2xkTTdtdlhodlJkKzg0SU5aZGlabmt6NmYrT21KelIiLCJtYWMiOiIyZTgzOTJiOTgyODQwMTNkYWNjZmZlMzQ1ZDFmZTY1NGExMmM4NjQ5YTQ1ZjVmYmRiOWIyODE5MGIxODA0OWE5IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:43:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ilh6SlJTZTVSQTBSTk5Rc1FZbGhoakE9PSIsInZhbHVlIjoiUkNrbVpKb010ZXVHT0JjSVR0TXFCZEhRS3gzZVd0OVhRZEdlTGlwV3d3bEJUWGwrdGR3a2JqdzREdFJ3THhCWFU4cmRZYzQxME9vYjRlOUlXSTZvRTlyUUxTdWlPcFFja295SU96TGZ3K1B0YVRaQkhmbVd0OGdjbTN1L2FGVzdrNEM4ZjNaTkZWejFyd0xzMk1VTEdkOWVLQ0ZoZDlrQ0I2VUlxTUhKMWl1dW5JMGQ1Yk1pZ2ZzRWFSM3BTaFhPMVRRemZRN09BOEtQRDhjQzJJRU0wUS9JU1hSSWtIcThud01OT0hBTThObTluUHNWd1dNYnJEZEtsUVRJbG9MMUZ0N2FBVVlCSk1XajNERDVTL0pra20wTjM4T2pxYnMzNU0zc0VGZVY4R3RDNGhpVjNYRW1VVUFXSUdWWnZsbDZ3c2hJejdYVGJvbStTQjNiSlhUZjk4RkVNK2JQajcyUnBYMmhMNmNRQWZtczZKZHZmeHlxNjJFUlA3Z0d3UmRUNXNWTER1ZlpXUHFpK3VyemZMcWJtbmM2eUxNaGVIOWZ6ekkxYm5CY0RkQXNZSWpxTDRlZkwybUdtcTdQVHcrUmd4aGIxbk1scjZoeGN1YWtBZHhzUDltKzRVSjh6R0hGd2RzZ0l2MnFpanQ4cmEyUTNaMlgrZWRkMjlLN2hDMWgiLCJtYWMiOiIwMDE2YjNjOTRjMDUxNzgwZDJiOTE1NTA3NWU3ZjM5MTE0ODRiODg1OTJmMzk3ZDlhYzljY2E2ZTkzZTcwMGMyIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:43:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVpMHlvakpZSUMxYnZmMjY5STY0ZXc9PSIsInZhbHVlIjoiK1VQSEpMYXRpVlQyeEsrYlBaVVp4TWhlTFZhdFdCTVFhL0NwU0xhK1UrK0NxdFFGRGEzQ2REODUvVmhtNEd3QlBwTzFMLzhLc3VOUzdwNEVvbU9xNzd0SkVRRHYzTGpTbFdheUlXNmJYMENPbEZId25CYWFuM09BNDJhOE4rOGZmM2JydFo5SkNKT29iNEZBOFVVVXRLY01HQkpUS0UxK3prUXFUd096dklMK1g4RnZ0UXBvUW43anZRTUFSZW9NM2hFZnFtSzYyMCswaW9LTm4xc3FySHNYOTlERkFoSGVpYnZXRFU0eElNK0hkc3loZWIrYWdNMThXdFZZZ0t6RVlTT3pDUFlTS2VlUlRYL0NBRDgwQTBoZzRLcGpEaEt0ZmMvOWYxeG9GcFpUNzRuRFBkS2pFUGYzaGZiQ2Rsa3BOL0duempNc1BENERVMnNubEgwTGVQSTNaaW4wVEVkaWJnSmdqNTJ5bFp3SUgwL2Q2dG9CMnFnQllJaC8zaTg2UkJ2M1lCQ3NZZlBGOGNqYlhXMkNuZXhqN3h5aS96eVBPSTRqVi9aK2NJTHlXSGs1WVZNK3BnbDh1cUJ3cFdUclBGL0ZRWnhnQVREc0hkM0R3Wm8xZjdVOXBhcFI5WWkvU2xkTTdtdlhodlJkKzg0SU5aZGlabmt6NmYrT21KelIiLCJtYWMiOiIyZTgzOTJiOTgyODQwMTNkYWNjZmZlMzQ1ZDFmZTY1NGExMmM4NjQ5YTQ1ZjVmYmRiOWIyODE5MGIxODA0OWE5IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:43:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ilh6SlJTZTVSQTBSTk5Rc1FZbGhoakE9PSIsInZhbHVlIjoiUkNrbVpKb010ZXVHT0JjSVR0TXFCZEhRS3gzZVd0OVhRZEdlTGlwV3d3bEJUWGwrdGR3a2JqdzREdFJ3THhCWFU4cmRZYzQxME9vYjRlOUlXSTZvRTlyUUxTdWlPcFFja295SU96TGZ3K1B0YVRaQkhmbVd0OGdjbTN1L2FGVzdrNEM4ZjNaTkZWejFyd0xzMk1VTEdkOWVLQ0ZoZDlrQ0I2VUlxTUhKMWl1dW5JMGQ1Yk1pZ2ZzRWFSM3BTaFhPMVRRemZRN09BOEtQRDhjQzJJRU0wUS9JU1hSSWtIcThud01OT0hBTThObTluUHNWd1dNYnJEZEtsUVRJbG9MMUZ0N2FBVVlCSk1XajNERDVTL0pra20wTjM4T2pxYnMzNU0zc0VGZVY4R3RDNGhpVjNYRW1VVUFXSUdWWnZsbDZ3c2hJejdYVGJvbStTQjNiSlhUZjk4RkVNK2JQajcyUnBYMmhMNmNRQWZtczZKZHZmeHlxNjJFUlA3Z0d3UmRUNXNWTER1ZlpXUHFpK3VyemZMcWJtbmM2eUxNaGVIOWZ6ekkxYm5CY0RkQXNZSWpxTDRlZkwybUdtcTdQVHcrUmd4aGIxbk1scjZoeGN1YWtBZHhzUDltKzRVSjh6R0hGd2RzZ0l2MnFpanQ4cmEyUTNaMlgrZWRkMjlLN2hDMWgiLCJtYWMiOiIwMDE2YjNjOTRjMDUxNzgwZDJiOTE1NTA3NWU3ZjM5MTE0ODRiODg1OTJmMzk3ZDlhYzljY2E2ZTkzZTcwMGMyIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:43:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-188358912\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1355925010 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qIxGkzrR170Y3S3ZV9TiQSn8i4NIauirZ2hSxOQ6</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1355925010\", {\"maxDepth\":0})</script>\n"}}