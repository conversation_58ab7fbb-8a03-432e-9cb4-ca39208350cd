{"__meta": {"id": "X4f5c157293e60ed01d52c09e5d544bf9", "datetime": "2025-07-30 10:11:55", "utime": **********.615454, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753870314.702966, "end": **********.615527, "duration": 0.9125609397888184, "duration_str": "913ms", "measures": [{"label": "Booting", "start": 1753870314.702966, "relative_start": 0, "end": **********.516596, "relative_end": **********.516596, "duration": 0.8136301040649414, "duration_str": "814ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.516611, "relative_start": 0.****************, "end": **********.61553, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "98.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3038\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "nBgZ1KF7mTDsxK1tHa8DbzAILuMMOBiJYhvTpRV5", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-2133439022 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2133439022\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-826603547 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-826603547\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1008101739 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1008101739\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1869848264 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1869848264\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1594512179 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1594512179\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1457086812 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 10:11:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZwWjhMTlNQVFFKdkRTcExpMldqRGc9PSIsInZhbHVlIjoiU1JNeHQrOVI3NllmcEZTb2tSSXMrN3k1aDZmUnZ4WElKZllIMkNEdUFFWmRtODNaTVJVOVpLTDFTM04ra0VsRnFaRnZURzdvY3ZTTnBvSXo2K1d4S1dUOGxTQ3pJTFVoUjc1V1drbnlMTjRaYWtYUzROOVJJNEEyR052aVN6L3MrdGw1QWpmaHhQYU9ncmdDbEszQzJ2eTNEeUFmVVp4d1c3N3A3clVsS21Sc0k1eE5ESXBHRTBYdVY3MldYbUNTUldPeXFSZUx3UTNZcllJcENRekxwSkhid29KbDNUa2ZPSCs0QjZsamdSeWRTcS9Ob2lxTk9FMlZIZkR1cHEyaWh1TmJ0ajV3Nm1KeWdqU0E4ZGt3ZWQ0ZzRUaDk0ZWNFVFNFcWxlSU9waDlBQXdidlNNakxKUTNMUVBENDAvUHh3RTFaWnRJQlFnUy9yaldvTmdpa2pMellNWWZoN1Q0aXZkWUJ6Mk1la21qZElaZG8ycVhpS1JaYjFPcG1rRlE5ZWdXRXp6b0ZuS2hDS1J4TFVuYkxZNENFZzg1SWFKZ0V6RVBxMzZHbDNzT3ZETDFVWWs0cVFpcWdNcy9jS1BaaVY2aEJjc2tUSTF5c2ViQW0wYjZNSzVNSjJ4dzV6VjVOTGMvTkp2ZkIwWnFTeERjdlhxVlBsd05tQ0dDTzg2aHkiLCJtYWMiOiI2NjljNDgzOGZjM2JjN2ZkMTFjODk0MDZjNWExOWFmMzEwZjhlOTQxNTRhMjJhMmY4OWNjMjZlODRlN2VkN2Q2IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:11:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IllUcTdwV0FCOHRNcFlqdWtXQlBvbFE9PSIsInZhbHVlIjoiWnlmSy9JeHdnK2xldTBLUGxHQmtxSU1pLzFuZWY1L2t4VGw3MUtWRGd2QnhhMkIvQm1URzRlWmhMSTNyTi9DRmY1anpyTkVNY3hLZ1ZyZVo4NlMvc1dBeXZSQ2owTFppQkpUVkpwcDRvcjV2R0hJcGI0Vm84V1R5WWtzNm9DelRBbVlGbXA0bWs3YXh3SFI5Q2pvbTVXeXZDODVDaUx2eTlNNXVrSDUwSHYrNlZqWm5uWTlsTkt3SjF6K3o3MzNSM040YzNuRmo5ZEdRTTNCaFBMSmtZQXlzb3N1UzF5bGo4R015TGVPd0VPM2lQN0NEWWQvcTF0RTVLbU1BSWowWCsvTnF1OE5pSU9jTURGeVl0aEtyZDc4VWpaeHZNeng2U0RycWJ1SCtSWHQ4MSsvcWdzK090MFB4Q1lETHc2TDBYeS9DakoxTTRSelNtanpPdDFxT1JDTElEYVhNVCtnbEI1SUpHM3I4enJWTVNTVkE0ZkhaVjBYRDZjQ0R0SEdjRy9KTm5LcmhpSGticXowNEdaTUVMcU96cm9YdFl3ak51MFQ3L3hUYUpxc0EwWFlUYmJKbEJEVGl1SHVZNDlPTWpoeEd4bUV2Z0xqU0QxSG9sdkZqbzlwbk9CdkttSWV2eWRzUDBQcFRtbGNPcmhPVE5Pbi9Yb2JyOFJZOVVFd1ciLCJtYWMiOiJiMTRjYjRjMTNlY2EyOWFiOWZmNGI2YTYwZWJjYjhjYWJlNDE3MjdjYWNhODFlNTVmZDU4NjBmNDgxNGY5NzM0IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:11:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZwWjhMTlNQVFFKdkRTcExpMldqRGc9PSIsInZhbHVlIjoiU1JNeHQrOVI3NllmcEZTb2tSSXMrN3k1aDZmUnZ4WElKZllIMkNEdUFFWmRtODNaTVJVOVpLTDFTM04ra0VsRnFaRnZURzdvY3ZTTnBvSXo2K1d4S1dUOGxTQ3pJTFVoUjc1V1drbnlMTjRaYWtYUzROOVJJNEEyR052aVN6L3MrdGw1QWpmaHhQYU9ncmdDbEszQzJ2eTNEeUFmVVp4d1c3N3A3clVsS21Sc0k1eE5ESXBHRTBYdVY3MldYbUNTUldPeXFSZUx3UTNZcllJcENRekxwSkhid29KbDNUa2ZPSCs0QjZsamdSeWRTcS9Ob2lxTk9FMlZIZkR1cHEyaWh1TmJ0ajV3Nm1KeWdqU0E4ZGt3ZWQ0ZzRUaDk0ZWNFVFNFcWxlSU9waDlBQXdidlNNakxKUTNMUVBENDAvUHh3RTFaWnRJQlFnUy9yaldvTmdpa2pMellNWWZoN1Q0aXZkWUJ6Mk1la21qZElaZG8ycVhpS1JaYjFPcG1rRlE5ZWdXRXp6b0ZuS2hDS1J4TFVuYkxZNENFZzg1SWFKZ0V6RVBxMzZHbDNzT3ZETDFVWWs0cVFpcWdNcy9jS1BaaVY2aEJjc2tUSTF5c2ViQW0wYjZNSzVNSjJ4dzV6VjVOTGMvTkp2ZkIwWnFTeERjdlhxVlBsd05tQ0dDTzg2aHkiLCJtYWMiOiI2NjljNDgzOGZjM2JjN2ZkMTFjODk0MDZjNWExOWFmMzEwZjhlOTQxNTRhMjJhMmY4OWNjMjZlODRlN2VkN2Q2IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:11:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IllUcTdwV0FCOHRNcFlqdWtXQlBvbFE9PSIsInZhbHVlIjoiWnlmSy9JeHdnK2xldTBLUGxHQmtxSU1pLzFuZWY1L2t4VGw3MUtWRGd2QnhhMkIvQm1URzRlWmhMSTNyTi9DRmY1anpyTkVNY3hLZ1ZyZVo4NlMvc1dBeXZSQ2owTFppQkpUVkpwcDRvcjV2R0hJcGI0Vm84V1R5WWtzNm9DelRBbVlGbXA0bWs3YXh3SFI5Q2pvbTVXeXZDODVDaUx2eTlNNXVrSDUwSHYrNlZqWm5uWTlsTkt3SjF6K3o3MzNSM040YzNuRmo5ZEdRTTNCaFBMSmtZQXlzb3N1UzF5bGo4R015TGVPd0VPM2lQN0NEWWQvcTF0RTVLbU1BSWowWCsvTnF1OE5pSU9jTURGeVl0aEtyZDc4VWpaeHZNeng2U0RycWJ1SCtSWHQ4MSsvcWdzK090MFB4Q1lETHc2TDBYeS9DakoxTTRSelNtanpPdDFxT1JDTElEYVhNVCtnbEI1SUpHM3I4enJWTVNTVkE0ZkhaVjBYRDZjQ0R0SEdjRy9KTm5LcmhpSGticXowNEdaTUVMcU96cm9YdFl3ak51MFQ3L3hUYUpxc0EwWFlUYmJKbEJEVGl1SHVZNDlPTWpoeEd4bUV2Z0xqU0QxSG9sdkZqbzlwbk9CdkttSWV2eWRzUDBQcFRtbGNPcmhPVE5Pbi9Yb2JyOFJZOVVFd1ciLCJtYWMiOiJiMTRjYjRjMTNlY2EyOWFiOWZmNGI2YTYwZWJjYjhjYWJlNDE3MjdjYWNhODFlNTVmZDU4NjBmNDgxNGY5NzM0IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:11:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1457086812\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-453662743 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nBgZ1KF7mTDsxK1tHa8DbzAILuMMOBiJYhvTpRV5</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-453662743\", {\"maxDepth\":0})</script>\n"}}