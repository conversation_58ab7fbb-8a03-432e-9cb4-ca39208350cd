{"__meta": {"id": "X2fef400369a84495cca05fc9a63b0fd9", "datetime": "2025-07-30 10:33:29", "utime": **********.393245, "method": "GET", "uri": "/finance/sales/contacts/search?search=pa", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753871608.646416, "end": **********.393268, "duration": 0.7468521595001221, "duration_str": "747ms", "measures": [{"label": "Booting", "start": 1753871608.646416, "relative_start": 0, "end": **********.286532, "relative_end": **********.286532, "duration": 0.6401159763336182, "duration_str": "640ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.286548, "relative_start": 0.640131950378418, "end": **********.393285, "relative_end": 1.6927719116210938e-05, "duration": 0.10673713684082031, "duration_str": "107ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46728640, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1241\" onclick=\"\">app/Http/Controllers/FinanceController.php:1241-1333</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.014580000000000001, "accumulated_duration_str": "14.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3442101, "duration": 0.0071600000000000006, "duration_str": "7.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 49.108}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3632088, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 49.108, "width_percent": 6.584}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%pa%' or `email` like '%pa%' or `contact` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1257}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.36921, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1257", "source": "app/Http/Controllers/FinanceController.php:1257", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1257", "ajax": false, "filename": "FinanceController.php", "line": "1257"}, "connection": "radhe_same", "start_percent": 55.693, "width_percent": 13.992}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%pa%' or `email` like '%pa%' or `phone` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1283}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.375051, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1283", "source": "app/Http/Controllers/FinanceController.php:1283", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1283", "ajax": false, "filename": "FinanceController.php", "line": "1283"}, "connection": "radhe_same", "start_percent": 69.684, "width_percent": 23.594}, {"sql": "select * from `users` where `created_by` = 79 and `type` = 'client' and `is_active` = 1 and (`name` like '%pa%' or `email` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "client", "1", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1306}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3814461, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1306", "source": "app/Http/Controllers/FinanceController.php:1306", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1306", "ajax": false, "filename": "FinanceController.php", "line": "1306"}, "connection": "radhe_same", "start_percent": 93.278, "width_percent": 6.722}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-1551986745 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1551986745\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-747955379 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">pa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-747955379\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-52099603 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-52099603\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1396083365 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImdxNHQ4WnlPZ1ZjNDE2cDFCdWRvdlE9PSIsInZhbHVlIjoic3ZaLzVpaGJZVUphckI5ZDRvV01TYyszOG5Tck5wMHg4OGkydHl1d0F1ejZXS0E2Zk1PRWt0YTRHVHJ4YlhvWEM1OTRMKzh2TTA3NmdXVmRDYXp1QlUxQmFOcnlGaks2V05VOWF1V0xSbFVRMXVrZ1dTdUhoMEZZNjlPRzRzMDZNZnFGZlZpeWxOdXU2OGJ1STc2YnNwQ2Z4b29lZ0RTSXEyeWVUMVNaTmxRUG5HTHFNeDQrbWh6QWl0Y0VCcjNvRVlna0p2N01aQzdnVS81Y3VhRGZzK28vbW5ETHN6MWVETll3RmVoNzRXNWFMZTZ4VHM3VDNFOE1IQ3dIbzl0ek9VdURwU3ZIV084bjV0WVJsTVIyOXR0ZmtwWmxFVE5qUWFWV1Vab21QL1I3TitnWk9scnpqdi94NUFSWmJ2V09FOUtCYzF5K3N6NUF4bHl4bHlNYnZqOHhQanJtc3Flb3dpQ25Rck9oWWUxMC9Fc1pPZEVDeDEzeGVBazJkTUVzdUNSblI3N0poOTNhUnNaQ3JyamZqQzZJK2FaenZnVEFzYzVmR1ZkMzhscEhKVFA3SjZnVFJ6dzljQ0pybUw5emI1cUFHSC9uVnZlSC9vMTlsS01VKzdPT1AvNTFSRWxxcVZ4YUlnWHJ2SGprakZtVkpNdytKU0wrQW1JbWM5OEwiLCJtYWMiOiJiZjdiM2MzZTVjMGMxY2FjZWVkNzI4YzQzOGI1NWFlYjMzYTNkYjI5NDhjZjk0MzNhZDg0OGNlMGIxYmRmMzlmIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IktCK2tRbDA1anQzN00yVlhjaTJMcUE9PSIsInZhbHVlIjoiSlB3SHhlNnRSNk9pcE5weXZzVkVMeFJFVzNVMkNURjFDYTNLYVpnYXJMOFJOQWdzeUM1aDh2MVpXL2RuczNLOXg1dG5GNmlrNW5ObjZ5T3RFV0swcCszQkIzbXJsVTA2Z05PNmxUdkFpYjRWREVRVDFtS0VBWFV6TFovUkE1K1VCWnZ1bzc4c21nZmdDRGhGWlRCMkp0YmRDakFnTC9hWUxEWGVrSWNuQlJMOUJmV0pUQWQyMWZKb05EMnJUSjZjbTVkYitBdVhSdGtub3pnWWlIY2VhSHVhYjlXRVM3c3RPZlBEdkpPR2pyOFVSMXRrQkQwY0lmekwrRzFwZktSVWFZaS9LZjh1TnNnMlVjTHJoenE2YnlnNWpmTnBxZjNNT3FvNTU2czNFL3BSdHBkSDRjOXdJdlJuMkJVc1dtcW51eWpyb0UveUtVWjU2ZEhvRTRLSDRJVnZmc0FQV1czN1Juc2pXSEVlNU5neVJud0xzaEdrYzZOSG5ZVkVzNmlVaHhDZ2dqQ2xFakpyNlltSkhCWkhqbjhveE96WXBTSzJsVE9YVU02Uk5jZXdzNzJrS1BOQnU4MFJ1anZIR1F0WjhOUWJSYTFjVXNWYTY2dktoWEIrTlVieTlhTlIzNjFnWDZ1ZjJkN3Qvd2g2bVYzRzdBOCtuaE96dHBtaVVScEoiLCJtYWMiOiJkNmZmNzVmM2JjYWE5YjEwOWFmMWJhY2ZmYzcyMTIxZjg0OGQ5NjkwYWM5M2QzMTgxOTExMDE2ZTMwOWIzMmY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1396083365\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-442804122 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-442804122\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-696568529 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 10:33:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRUN2luRGRMRU1rZ0QxMW9MenkveVE9PSIsInZhbHVlIjoiVEtNdFZCN3oxZUtUTlo0NHhLT2JoZmdHVkI3enRwYVN3aDJyTDBZeGhsZHNYZmNNeVRIRWFwcWdmWmNXU1l2cVRweVMvTlRPNnVuUHJ0aUpLVDJSdEh1VlM3RGJGQ2QvNExtS0xvWFpvWXhqSmhXTEtYRWc2anJISllFS1JJb0cyZ1J2ZnljREwxZytvcWltN1hmU0VkN0xZbG91ZGRMbFhrWHRtaVRsNVdCSHhUUVZEbmJ1bm9XQ3VEczJDUHdwMWJOcGRDUU95Y0FkaXBjU255K1NjUHl1VHV0R3Q4a1R2SG02VTFNTmUyOEFaQ2k5VnlSZTZLS2pRRHFaajRvTlMyVjJOWUFuODZ3R0tDNEs2elRsMzBuMXIweFM0YlpONjhkNy9wZThmQ09XQUN1MjQxdW45bi9FSSsyZmNXdmNTY1hzaSsrTFpYZTEwTlVqditBMUsvWXl1MlRQNFhsVW8vYXYxUWFiOGJLcEl1cFRiOEhXOXdhVTg4dW9aRytqY0JkUC9LOXJkSnRPMVF0ZjV4QSttWHFQNXdLa3poQ3o1SkorVnQ2eEV4KzU4U0grTnIxeWY1WlpQd0FrWnpzU3p4ZXAxZHRqaWNRZWRKam1uajBjMkRjT2Qvb0ZTZS9lamY2WmlFd3E1aDJPcXRxKzlYbmtTaEt3Y1FENitzeVciLCJtYWMiOiJlNGI2YjZiZGM0Zjg5NDZhM2M1MDFiZWNiNmIxYTE4NDNhYmI4ZWQ4OTljZGI3ODA1YWNiMDFhZDVmNWY4N2E2IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:33:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InhsRTZtcVVyQ1FSYlJLMnlJQVVNS0E9PSIsInZhbHVlIjoickFCczJ4ZWxWaG5ZUm9FOUUxQ2VnVndnck94UmQ1Nk1lUllxc0x2ZWlCNGNaK09RTU1JRmpjWm1pYjhPbHk1V3gxZGhpYS9PWXhpWFhhWkh6VGdoVWtLTFVZZFBZa0JIRDZIcjBjcHJUTVVyZTBoSkw4L0JBSTI2akJhSFdVdExmVGtJdE14cGc5NXVhUmUxUjZ6eEs4am1DWTJZeE40RjluTHBBdDJpWnN6RGw4YWdveGp6dFpldGwvcHpncERaYldlUjc0UWNQZnA5SmUrV0JjK2JyY2t5Slg5TmltcDJUZm1uOUduWHFkYzFpamRmM0RFM2tiNDNuTmNNYUdqU0tkTzI1eEJyMjc1OUwwbU1QdEcwR1Rvb3JlbldrWC9DbDB4TDAzTlZiMzN5QVZ0K2hiOFlQWW5aRkcxeXJQTWFpS1pIQ0tvclpNYkduaitmQzkwRHdDcVJBbmt0NVEyMjJhaUkvOGJrQ0NPZTgxaFZXZGlGbmEvTExhbHFRMFkvY0tlMG8waVk3b05QbzBaVVQrdm8ydkxpNUlZeURnUm56Mm1wNnNLaGVuQ3dtbkk0cUcxWDk3eGsySldJdjMyRXVURzB1ejdHV0FFNDJVZllGOW1RMGsycVd1MUdGRDJucHZWL2xNaktnUFhsMFFJeGxjUU5nNWsvY0g1MXMxOFMiLCJtYWMiOiI2YzUwZDEwMTJhMGQ2NzE0MTlmNTMzMDlkNTFiMmRkMTRhMzFiMDYwNDhmMjNhZGIxYWE5OGU1YzNiMjBjY2U1IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:33:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRUN2luRGRMRU1rZ0QxMW9MenkveVE9PSIsInZhbHVlIjoiVEtNdFZCN3oxZUtUTlo0NHhLT2JoZmdHVkI3enRwYVN3aDJyTDBZeGhsZHNYZmNNeVRIRWFwcWdmWmNXU1l2cVRweVMvTlRPNnVuUHJ0aUpLVDJSdEh1VlM3RGJGQ2QvNExtS0xvWFpvWXhqSmhXTEtYRWc2anJISllFS1JJb0cyZ1J2ZnljREwxZytvcWltN1hmU0VkN0xZbG91ZGRMbFhrWHRtaVRsNVdCSHhUUVZEbmJ1bm9XQ3VEczJDUHdwMWJOcGRDUU95Y0FkaXBjU255K1NjUHl1VHV0R3Q4a1R2SG02VTFNTmUyOEFaQ2k5VnlSZTZLS2pRRHFaajRvTlMyVjJOWUFuODZ3R0tDNEs2elRsMzBuMXIweFM0YlpONjhkNy9wZThmQ09XQUN1MjQxdW45bi9FSSsyZmNXdmNTY1hzaSsrTFpYZTEwTlVqditBMUsvWXl1MlRQNFhsVW8vYXYxUWFiOGJLcEl1cFRiOEhXOXdhVTg4dW9aRytqY0JkUC9LOXJkSnRPMVF0ZjV4QSttWHFQNXdLa3poQ3o1SkorVnQ2eEV4KzU4U0grTnIxeWY1WlpQd0FrWnpzU3p4ZXAxZHRqaWNRZWRKam1uajBjMkRjT2Qvb0ZTZS9lamY2WmlFd3E1aDJPcXRxKzlYbmtTaEt3Y1FENitzeVciLCJtYWMiOiJlNGI2YjZiZGM0Zjg5NDZhM2M1MDFiZWNiNmIxYTE4NDNhYmI4ZWQ4OTljZGI3ODA1YWNiMDFhZDVmNWY4N2E2IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:33:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InhsRTZtcVVyQ1FSYlJLMnlJQVVNS0E9PSIsInZhbHVlIjoickFCczJ4ZWxWaG5ZUm9FOUUxQ2VnVndnck94UmQ1Nk1lUllxc0x2ZWlCNGNaK09RTU1JRmpjWm1pYjhPbHk1V3gxZGhpYS9PWXhpWFhhWkh6VGdoVWtLTFVZZFBZa0JIRDZIcjBjcHJUTVVyZTBoSkw4L0JBSTI2akJhSFdVdExmVGtJdE14cGc5NXVhUmUxUjZ6eEs4am1DWTJZeE40RjluTHBBdDJpWnN6RGw4YWdveGp6dFpldGwvcHpncERaYldlUjc0UWNQZnA5SmUrV0JjK2JyY2t5Slg5TmltcDJUZm1uOUduWHFkYzFpamRmM0RFM2tiNDNuTmNNYUdqU0tkTzI1eEJyMjc1OUwwbU1QdEcwR1Rvb3JlbldrWC9DbDB4TDAzTlZiMzN5QVZ0K2hiOFlQWW5aRkcxeXJQTWFpS1pIQ0tvclpNYkduaitmQzkwRHdDcVJBbmt0NVEyMjJhaUkvOGJrQ0NPZTgxaFZXZGlGbmEvTExhbHFRMFkvY0tlMG8waVk3b05QbzBaVVQrdm8ydkxpNUlZeURnUm56Mm1wNnNLaGVuQ3dtbkk0cUcxWDk3eGsySldJdjMyRXVURzB1ejdHV0FFNDJVZllGOW1RMGsycVd1MUdGRDJucHZWL2xNaktnUFhsMFFJeGxjUU5nNWsvY0g1MXMxOFMiLCJtYWMiOiI2YzUwZDEwMTJhMGQ2NzE0MTlmNTMzMDlkNTFiMmRkMTRhMzFiMDYwNDhmMjNhZGIxYWE5OGU1YzNiMjBjY2U1IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:33:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-696568529\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-373006021 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-373006021\", {\"maxDepth\":0})</script>\n"}}