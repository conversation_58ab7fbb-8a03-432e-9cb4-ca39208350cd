{"__meta": {"id": "X755a1c0366b1ceaa95a6c19fe8671197", "datetime": "2025-07-30 10:25:53", "utime": **********.806309, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753871152.340742, "end": **********.806351, "duration": 1.465608835220337, "duration_str": "1.47s", "measures": [{"label": "Booting", "start": 1753871152.340742, "relative_start": 0, "end": **********.710129, "relative_end": **********.710129, "duration": 1.369386911392212, "duration_str": "1.37s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.710146, "relative_start": 1.****************, "end": **********.806355, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "96.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3038\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ZwoyiLnMX2arya4eqLI3Kvj8EcwmRqaQYjR2gCv2", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1304344843 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1304344843\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-606677123 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-606677123\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1291353244 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1291353244\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-832563535 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-832563535\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-190342651 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-190342651\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-554857769 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 10:25:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im90UkhHTEpUUjhzUG1uMVp2dFdFWGc9PSIsInZhbHVlIjoiR3lxT3gyaTFhTnNoZjZNNVhIdVZ5VlFWR1lFVVR6dC9XQUpyMnFReWgybXdtUWZSTVZYTHZrYjdzYmppMnVJNjhaeUs4T04vclI2VVlOZnNaMG4rYU9PZE9URmZtVW1nYnA2ejA0Q3JRZ3Noc3NERnR3Q0o0NnJnQlJubVhnUnNrR1owSVJpL1UxbjQ1OCtzQldJWkVod3o0YkJBSEpOYUJYVHdNUzU0MDJ0NGtwS1pWSGNpVE05ODAzckxMT25EVERWdVMvbVo2Y1picHlPcDdMdm9FNHEyU0hjWk1qT2NRNmZpbDBkV0gySSsxTDBzQVpEenRNOUU3T1Fzc3hJbThCSUlDU0Q5a2piUk5CYlZOVHZITzJUMzhkd1VjOHBTcWJ3OXpGU1E1N3NrMEFUVFMreWRJeE0xT2g4RWIwMWUrUlhpS1IzY3VPN05WcGloaDNIQnJzZEZiaThJK3d3TjN5UndoQTFiU1ptU0pMYlQyeUNDY2MrWDNpSGJPdzFTaC9TRHY1anRYaGJ1S1d3L05NZkQ4T3AvcElodXRHdkZ3Si9vWXlxTmsvYVlvUy9rRHdQVzhMY2o2Ylp5cXNZcXdLa05HZlpOdGhHWmNKYUlKblFyK2pXUkt0Z0hmbVpyMVZjY1ZQL2tFNmtYT3JUc2N6NmRIeC80R0Y0UGpEQlUiLCJtYWMiOiJjM2RkMTI4NDA4ZTczMTAyYjE3MjI0NzRkMjQxMTQ3OTFlNzkxODMwNjViMzkxZDc1Yjc1YTZmYzM4YjI0ZWYyIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:25:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImkvL1FUTGY2V2hScklkQTdob0dISUE9PSIsInZhbHVlIjoic2EwNGlraXcybXFoYmNtaDFXcmg4aHpVTDRXMkloMHowZEE2Q1J1SldFeUg2YWgxN0E1T1hManpzS0pPcDc1YkJpMWliVGtlVnNBRW9waHVMSy9hRFM3bSttYXNXZXlKMTN2QVlIWFdDcHllczBsbXRxNjJDQk5QRmpPZXFuVTRSZUJJcE5ta1lSaE1DM0Q3TFBnd1hRTFVNWlJvcTcrYjBGelczVE44VzNNdXg4ZVNwSk41TytvUUxpb2REbjdWWXdwblZEWTRsUmpUQzlKZVFrV3M1Q2NLektUOVBDUzBRQytQNGlQam5Pc1huSlJGYVl0TWh2NjRsM09TMFlqQ3NvT24zUlpuNjNzT0x6ZFBVZi9yMGhXTVJzcTl1YUxaRU5hNXJCR3BacGVHbVNqUVF6bUNkU3ljV2x4ZmtDYlFuVzdLbFpZd1NLRFRHU0pJR2FRWUFPQm9lbEpQUTZjNVljaTEvTWxPK1RIMjhIa0JTYmZFNFBMQzNQU3RLMjQrVk5ERlc5Q1NPcStWOGVoZEJKYlArRjZ1Z0tSVnlUakYydndxdWZZRDZpNkFqZzRWNU5MUUh0MlArd3hBMnFkamUzSWdiaTlWbjRnUzUzUDVVMDNwQ2JSNTM1YVVqSWRqV2hEYmVMRUFmNUc1MzZNYjVQa3NHWVIxM3B5WkYyQXQiLCJtYWMiOiIwMjQwZDM4NjEzNGVmNTI5MjNlNWZlY2U5Yjk5ZjRiZDA0N2Q0NTEwMzM1Nzk1MTI5ZGJlZTRkMzEyNTFjZTEwIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:25:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im90UkhHTEpUUjhzUG1uMVp2dFdFWGc9PSIsInZhbHVlIjoiR3lxT3gyaTFhTnNoZjZNNVhIdVZ5VlFWR1lFVVR6dC9XQUpyMnFReWgybXdtUWZSTVZYTHZrYjdzYmppMnVJNjhaeUs4T04vclI2VVlOZnNaMG4rYU9PZE9URmZtVW1nYnA2ejA0Q3JRZ3Noc3NERnR3Q0o0NnJnQlJubVhnUnNrR1owSVJpL1UxbjQ1OCtzQldJWkVod3o0YkJBSEpOYUJYVHdNUzU0MDJ0NGtwS1pWSGNpVE05ODAzckxMT25EVERWdVMvbVo2Y1picHlPcDdMdm9FNHEyU0hjWk1qT2NRNmZpbDBkV0gySSsxTDBzQVpEenRNOUU3T1Fzc3hJbThCSUlDU0Q5a2piUk5CYlZOVHZITzJUMzhkd1VjOHBTcWJ3OXpGU1E1N3NrMEFUVFMreWRJeE0xT2g4RWIwMWUrUlhpS1IzY3VPN05WcGloaDNIQnJzZEZiaThJK3d3TjN5UndoQTFiU1ptU0pMYlQyeUNDY2MrWDNpSGJPdzFTaC9TRHY1anRYaGJ1S1d3L05NZkQ4T3AvcElodXRHdkZ3Si9vWXlxTmsvYVlvUy9rRHdQVzhMY2o2Ylp5cXNZcXdLa05HZlpOdGhHWmNKYUlKblFyK2pXUkt0Z0hmbVpyMVZjY1ZQL2tFNmtYT3JUc2N6NmRIeC80R0Y0UGpEQlUiLCJtYWMiOiJjM2RkMTI4NDA4ZTczMTAyYjE3MjI0NzRkMjQxMTQ3OTFlNzkxODMwNjViMzkxZDc1Yjc1YTZmYzM4YjI0ZWYyIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:25:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImkvL1FUTGY2V2hScklkQTdob0dISUE9PSIsInZhbHVlIjoic2EwNGlraXcybXFoYmNtaDFXcmg4aHpVTDRXMkloMHowZEE2Q1J1SldFeUg2YWgxN0E1T1hManpzS0pPcDc1YkJpMWliVGtlVnNBRW9waHVMSy9hRFM3bSttYXNXZXlKMTN2QVlIWFdDcHllczBsbXRxNjJDQk5QRmpPZXFuVTRSZUJJcE5ta1lSaE1DM0Q3TFBnd1hRTFVNWlJvcTcrYjBGelczVE44VzNNdXg4ZVNwSk41TytvUUxpb2REbjdWWXdwblZEWTRsUmpUQzlKZVFrV3M1Q2NLektUOVBDUzBRQytQNGlQam5Pc1huSlJGYVl0TWh2NjRsM09TMFlqQ3NvT24zUlpuNjNzT0x6ZFBVZi9yMGhXTVJzcTl1YUxaRU5hNXJCR3BacGVHbVNqUVF6bUNkU3ljV2x4ZmtDYlFuVzdLbFpZd1NLRFRHU0pJR2FRWUFPQm9lbEpQUTZjNVljaTEvTWxPK1RIMjhIa0JTYmZFNFBMQzNQU3RLMjQrVk5ERlc5Q1NPcStWOGVoZEJKYlArRjZ1Z0tSVnlUakYydndxdWZZRDZpNkFqZzRWNU5MUUh0MlArd3hBMnFkamUzSWdiaTlWbjRnUzUzUDVVMDNwQ2JSNTM1YVVqSWRqV2hEYmVMRUFmNUc1MzZNYjVQa3NHWVIxM3B5WkYyQXQiLCJtYWMiOiIwMjQwZDM4NjEzNGVmNTI5MjNlNWZlY2U5Yjk5ZjRiZDA0N2Q0NTEwMzM1Nzk1MTI5ZGJlZTRkMzEyNTFjZTEwIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:25:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-554857769\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1572950904 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZwoyiLnMX2arya4eqLI3Kvj8EcwmRqaQYjR2gCv2</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1572950904\", {\"maxDepth\":0})</script>\n"}}