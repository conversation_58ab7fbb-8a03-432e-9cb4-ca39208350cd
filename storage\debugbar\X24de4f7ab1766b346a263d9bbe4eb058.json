{"__meta": {"id": "X24de4f7ab1766b346a263d9bbe4eb058", "datetime": "2025-07-30 10:11:53", "utime": **********.649278, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753870312.248182, "end": **********.649339, "duration": 1.4011569023132324, "duration_str": "1.4s", "measures": [{"label": "Booting", "start": 1753870312.248182, "relative_start": 0, "end": **********.551828, "relative_end": **********.551828, "duration": 1.3036458492279053, "duration_str": "1.3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.551861, "relative_start": 1.****************, "end": **********.649343, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "97.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3038\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "kcL1bGTZWMdJfLNMzJTJGCHHOX3Ogzn9ron9uYva", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1491028691 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1491028691\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1587901714 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1587901714\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-339566936 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-339566936\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-619579576 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-619579576\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-819666796 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-819666796\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-411915424 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 10:11:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikx0cXJ4WnpmUDdGSVBlaU1oMlRicHc9PSIsInZhbHVlIjoiWm8rK1krMHdZQURrTjlGNlJsbTR6di9WdHJObHdSUHY5VmlxT21lVk81N2k5b3B4UmdlZ3RWdmZYbDMvYlN5VVNuV2hmUzlzSEplU2xramtlUjNjODFGdml2amFtQStjTEtWS29ycWhzWGpPbFBiWFdsR0VDQXRLemREbmNtb2I4OGQ4ZjlwKzJPL2VOSVZkRnNBaW9COVBUbjZyQmlSQUN6ZmRaeWsrVnNiRjZIZjUvYklaMWUwVXRna2krZzlPUTBuWVJYcVo4RDNFM0Q3TnlvSm9KbVVEcStTVUxLMFFKNnE0ODZENUNLcDdnbWsrSGRmbm5vVlY1c1F3SXJHNUtpNVZISUxoVmlzenZtNzJZVGxVaTZnM29xOG9aMVM1SHpjU3FHUm5DM3UzejVHVHVubFRWam5wUC9YOExoR2dLYURVRGZMNVBEbGJoZjNrbnBLZEpidkQrRUMvTHY5WTltVy9rZ0NGdDdkYmowVkNFZEVnNDAxV3VVY2ZJVU1DWEVPa3dpeFZuMm5wZHlRQzVOS3pKbzFOTFhLV25hWCtRR2NsdkNRRDNwQldUUlkrYVc5WXozejVoVGRqMWZ5Q2ZjQjQvYkgyejJJeDNaSTh6cUo4eUg2TFhldHJjQ1hWVU5zVTVjUFhxeHllYzk5ODg5ZkcxT0VBS2k5NjlFdDEiLCJtYWMiOiI3Njc5MjU4ZWQ2MTBhMGY4MjZkYTA4NmVkNTkzNTZkYTFlN2ZmMzAxNmRjMzA0NzZlOTcwYTBjN2FmOWQ5MGY2IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:11:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjJscE1jck5OR2drU0tURHI1cEJCQ3c9PSIsInZhbHVlIjoiQTVaeitUUGViK1h0VVJGc21IQ0M4ZVdValVaUXFLdkNDaWxyYU1oQW43SDk0TlYvc0N0MmhPK3o1ajUvdVBpMWdQdmkrU0xiRWk3clk3UCtnc1JTU0VWL211UnN1MXh3WjdCYS9iUjNmdE9jOUgxYkxzWk9qZXMvWC9JMDhETlQwby8rVzZhREN1Skpodk41dFFmM0oxNis1OXIxblZPRldvaFIxU09kYVdzN21NVU85WTAyQjE4RXptV1I3NjFWbU04YTJwNTU0cWt2eWNzQ0s5eUFvczg4NVJYdGlEclVKT0lwZ1JCWWc1YUtVV2dDcFBjYU9LTjJPYzJjbnZIOW9nSy94UU44V2EyaHIwZmhsdWVZQklQbm95VnMwUGJlTGYrWXZCclBYeXp2ckV6V1FaRm1hazVuOUc2eWVGZ29pRC9LWmxjYW12YklJNGNEYWpGQkcvYU4zUHU0NnJjNjJ5QTVWWVQ2bTdNYzlXUzF5Y09UbmtraGczV1kxbUF2L01KQmt0bjVDSk55dkV6eXgwa2tsRDdVOHlFOHpWZmRXOXVINUpUN2lCWFYyTXlTSm94K25mNGVCOWJRYXpBUkxPb2ZGVGpsMWpBQXI4a0QzOGE0RFpnRW44Zm54NG5iQkxnUzAySXB1RWJodDY4VWgyZWduVEdJb2hZQlc1NkciLCJtYWMiOiI1YjJjY2E2ZjE4NjExYWE1NWE1YmY3OGEzZmNkNzEyODBjNDljOTYzNGJhYjE1NDhiNWEzY2FiNzFmMjc3NzVmIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:11:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikx0cXJ4WnpmUDdGSVBlaU1oMlRicHc9PSIsInZhbHVlIjoiWm8rK1krMHdZQURrTjlGNlJsbTR6di9WdHJObHdSUHY5VmlxT21lVk81N2k5b3B4UmdlZ3RWdmZYbDMvYlN5VVNuV2hmUzlzSEplU2xramtlUjNjODFGdml2amFtQStjTEtWS29ycWhzWGpPbFBiWFdsR0VDQXRLemREbmNtb2I4OGQ4ZjlwKzJPL2VOSVZkRnNBaW9COVBUbjZyQmlSQUN6ZmRaeWsrVnNiRjZIZjUvYklaMWUwVXRna2krZzlPUTBuWVJYcVo4RDNFM0Q3TnlvSm9KbVVEcStTVUxLMFFKNnE0ODZENUNLcDdnbWsrSGRmbm5vVlY1c1F3SXJHNUtpNVZISUxoVmlzenZtNzJZVGxVaTZnM29xOG9aMVM1SHpjU3FHUm5DM3UzejVHVHVubFRWam5wUC9YOExoR2dLYURVRGZMNVBEbGJoZjNrbnBLZEpidkQrRUMvTHY5WTltVy9rZ0NGdDdkYmowVkNFZEVnNDAxV3VVY2ZJVU1DWEVPa3dpeFZuMm5wZHlRQzVOS3pKbzFOTFhLV25hWCtRR2NsdkNRRDNwQldUUlkrYVc5WXozejVoVGRqMWZ5Q2ZjQjQvYkgyejJJeDNaSTh6cUo4eUg2TFhldHJjQ1hWVU5zVTVjUFhxeHllYzk5ODg5ZkcxT0VBS2k5NjlFdDEiLCJtYWMiOiI3Njc5MjU4ZWQ2MTBhMGY4MjZkYTA4NmVkNTkzNTZkYTFlN2ZmMzAxNmRjMzA0NzZlOTcwYTBjN2FmOWQ5MGY2IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:11:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjJscE1jck5OR2drU0tURHI1cEJCQ3c9PSIsInZhbHVlIjoiQTVaeitUUGViK1h0VVJGc21IQ0M4ZVdValVaUXFLdkNDaWxyYU1oQW43SDk0TlYvc0N0MmhPK3o1ajUvdVBpMWdQdmkrU0xiRWk3clk3UCtnc1JTU0VWL211UnN1MXh3WjdCYS9iUjNmdE9jOUgxYkxzWk9qZXMvWC9JMDhETlQwby8rVzZhREN1Skpodk41dFFmM0oxNis1OXIxblZPRldvaFIxU09kYVdzN21NVU85WTAyQjE4RXptV1I3NjFWbU04YTJwNTU0cWt2eWNzQ0s5eUFvczg4NVJYdGlEclVKT0lwZ1JCWWc1YUtVV2dDcFBjYU9LTjJPYzJjbnZIOW9nSy94UU44V2EyaHIwZmhsdWVZQklQbm95VnMwUGJlTGYrWXZCclBYeXp2ckV6V1FaRm1hazVuOUc2eWVGZ29pRC9LWmxjYW12YklJNGNEYWpGQkcvYU4zUHU0NnJjNjJ5QTVWWVQ2bTdNYzlXUzF5Y09UbmtraGczV1kxbUF2L01KQmt0bjVDSk55dkV6eXgwa2tsRDdVOHlFOHpWZmRXOXVINUpUN2lCWFYyTXlTSm94K25mNGVCOWJRYXpBUkxPb2ZGVGpsMWpBQXI4a0QzOGE0RFpnRW44Zm54NG5iQkxnUzAySXB1RWJodDY4VWgyZWduVEdJb2hZQlc1NkciLCJtYWMiOiI1YjJjY2E2ZjE4NjExYWE1NWE1YmY3OGEzZmNkNzEyODBjNDljOTYzNGJhYjE1NDhiNWEzY2FiNzFmMjc3NzVmIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:11:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-411915424\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2021466587 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kcL1bGTZWMdJfLNMzJTJGCHHOX3Ogzn9ron9uYva</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2021466587\", {\"maxDepth\":0})</script>\n"}}