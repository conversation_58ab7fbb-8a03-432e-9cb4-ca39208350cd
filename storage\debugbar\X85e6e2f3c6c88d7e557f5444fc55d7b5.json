{"__meta": {"id": "X85e6e2f3c6c88d7e557f5444fc55d7b5", "datetime": "2025-07-30 10:33:31", "utime": **********.213713, "method": "GET", "uri": "/finance/sales/contacts/lead/11", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753871610.424637, "end": **********.213741, "duration": 0.7891039848327637, "duration_str": "789ms", "measures": [{"label": "Booting", "start": 1753871610.424637, "relative_start": 0, "end": **********.120275, "relative_end": **********.120275, "duration": 0.6956379413604736, "duration_str": "696ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.120314, "relative_start": 0.6956768035888672, "end": **********.213745, "relative_end": 4.0531158447265625e-06, "duration": 0.09343123435974121, "duration_str": "93.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46729184, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/{type}/{id}", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@getContactDetails", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.get-contact-details", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1338\" onclick=\"\">app/Http/Controllers/FinanceController.php:1338-1415</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00739, "accumulated_duration_str": "7.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.180425, "duration": 0.00441, "duration_str": "4.41ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 59.675}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1960049, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 59.675, "width_percent": 10.69}, {"sql": "select * from `leads` where `id` = '11' and `created_by` = 79 and `is_active` = 1 limit 1", "type": "query", "params": [], "bindings": ["11", "79", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1364}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2011049, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1364", "source": "app/Http/Controllers/FinanceController.php:1364", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1364", "ajax": false, "filename": "FinanceController.php", "line": "1364"}, "connection": "radhe_same", "start_percent": 70.365, "width_percent": 29.635}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/lead/11", "status_code": "<pre class=sf-dump id=sf-dump-1949557953 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1949557953\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1700273446 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1700273446\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1464479768 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1464479768\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6InRUN2luRGRMRU1rZ0QxMW9MenkveVE9PSIsInZhbHVlIjoiVEtNdFZCN3oxZUtUTlo0NHhLT2JoZmdHVkI3enRwYVN3aDJyTDBZeGhsZHNYZmNNeVRIRWFwcWdmWmNXU1l2cVRweVMvTlRPNnVuUHJ0aUpLVDJSdEh1VlM3RGJGQ2QvNExtS0xvWFpvWXhqSmhXTEtYRWc2anJISllFS1JJb0cyZ1J2ZnljREwxZytvcWltN1hmU0VkN0xZbG91ZGRMbFhrWHRtaVRsNVdCSHhUUVZEbmJ1bm9XQ3VEczJDUHdwMWJOcGRDUU95Y0FkaXBjU255K1NjUHl1VHV0R3Q4a1R2SG02VTFNTmUyOEFaQ2k5VnlSZTZLS2pRRHFaajRvTlMyVjJOWUFuODZ3R0tDNEs2elRsMzBuMXIweFM0YlpONjhkNy9wZThmQ09XQUN1MjQxdW45bi9FSSsyZmNXdmNTY1hzaSsrTFpYZTEwTlVqditBMUsvWXl1MlRQNFhsVW8vYXYxUWFiOGJLcEl1cFRiOEhXOXdhVTg4dW9aRytqY0JkUC9LOXJkSnRPMVF0ZjV4QSttWHFQNXdLa3poQ3o1SkorVnQ2eEV4KzU4U0grTnIxeWY1WlpQd0FrWnpzU3p4ZXAxZHRqaWNRZWRKam1uajBjMkRjT2Qvb0ZTZS9lamY2WmlFd3E1aDJPcXRxKzlYbmtTaEt3Y1FENitzeVciLCJtYWMiOiJlNGI2YjZiZGM0Zjg5NDZhM2M1MDFiZWNiNmIxYTE4NDNhYmI4ZWQ4OTljZGI3ODA1YWNiMDFhZDVmNWY4N2E2IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InhsRTZtcVVyQ1FSYlJLMnlJQVVNS0E9PSIsInZhbHVlIjoickFCczJ4ZWxWaG5ZUm9FOUUxQ2VnVndnck94UmQ1Nk1lUllxc0x2ZWlCNGNaK09RTU1JRmpjWm1pYjhPbHk1V3gxZGhpYS9PWXhpWFhhWkh6VGdoVWtLTFVZZFBZa0JIRDZIcjBjcHJUTVVyZTBoSkw4L0JBSTI2akJhSFdVdExmVGtJdE14cGc5NXVhUmUxUjZ6eEs4am1DWTJZeE40RjluTHBBdDJpWnN6RGw4YWdveGp6dFpldGwvcHpncERaYldlUjc0UWNQZnA5SmUrV0JjK2JyY2t5Slg5TmltcDJUZm1uOUduWHFkYzFpamRmM0RFM2tiNDNuTmNNYUdqU0tkTzI1eEJyMjc1OUwwbU1QdEcwR1Rvb3JlbldrWC9DbDB4TDAzTlZiMzN5QVZ0K2hiOFlQWW5aRkcxeXJQTWFpS1pIQ0tvclpNYkduaitmQzkwRHdDcVJBbmt0NVEyMjJhaUkvOGJrQ0NPZTgxaFZXZGlGbmEvTExhbHFRMFkvY0tlMG8waVk3b05QbzBaVVQrdm8ydkxpNUlZeURnUm56Mm1wNnNLaGVuQ3dtbkk0cUcxWDk3eGsySldJdjMyRXVURzB1ejdHV0FFNDJVZllGOW1RMGsycVd1MUdGRDJucHZWL2xNaktnUFhsMFFJeGxjUU5nNWsvY0g1MXMxOFMiLCJtYWMiOiI2YzUwZDEwMTJhMGQ2NzE0MTlmNTMzMDlkNTFiMmRkMTRhMzFiMDYwNDhmMjNhZGIxYWE5OGU1YzNiMjBjY2U1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1222616204 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1222616204\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1933709188 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 10:33:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFvM1pkU2QvYkNmV0pXTFY5aWk3MWc9PSIsInZhbHVlIjoiTXhRNGlaRGd1QXZGRHFWTHBhclJRMUF3QUZyY0tWM3FROFpnSXBPcU1aY2RpdXZwRnZQYUFERkplZytxZWhBMks0S0lndm1Ya01NVUMxU0RmZmE5b3dxRDRHdXZib09wbS8yWVYzZWova1JwZjNSZkNuTTZZeG04dkpLaFFRdzYrMElScjA4Mm95dlZHK2Uvb0dYVTd6bFRjMkJRc2xMZVBZVWtvd1F2b1gvS0Y2QndiaU03MkdUcnhsank1TExHcUFoeFpTWjFaR1ZFdERtKzRrTXNRSHg1NlBlMFdwRHY5TlljLzJPN2dDc0ZNUjVhdnFlcW16eTZoa09kMG1MckQxZEdaUll3TWlwTFVpVFdKM2FHaFRYcXBicDhWOE5ZS1ZwU1Fob0pnMTBzNUhPWmRhY2J5bDJOSUlxWURoT0RJdExXQlkrWmpqVzNiaVp0Y1R1cCtYUDBpRWFvbWxHam1LQTdWV09LbnAxYWhCM2tYUlZJMktpSWVyTEF4Mk0xMWFaeC9CNDdjNUVmZjk2akIwMXVkREJrZkRRY09ta3dINjJvWmlheENzd1ZwQmhVbi91RTAwYlNXamNiTzg1VkwreTBnTFkxcHJYV0FkcDF3WW9qVDFKRW5QNXJrbjZXOXZtUG9PU04yOU1JZ0N0djZtTlJqVFZHaFZzZDAyMnIiLCJtYWMiOiJlMDBiYmE1ZTZiOTI1YmFiMDhkZTMzNThiYzUzZDlkOTE2MzgwNmFlN2M0ZDUzZWZhMzJlNjFkOTliM2VlZjRiIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:33:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkRBak5mdUtzRWFadGxKUk1VM3k2QXc9PSIsInZhbHVlIjoiK2gvaHkzWEhvYnRFelhRQnJOTVVMRTRCdFJoZTZlVDJYWWRndmZWejAySWJYQnlRZjRhcHVrNk5mdWdQNHFHdnRPZ3NoYmJxWEtucjMyNGpvY3hVTE54TmxTWXVsUVJXRXJCSFl3ZjlNZ1IvLzVTS3FoMWk1SDZEaGJCWnlsY3RsZmdmOGZxK3pTRmJUVWFORTRiSWZtZkM2MzZ5SmhYWENMTTRVc3FmM1QyMWQ3ZURLMTZkTHk2NzVDOE1TVm9tYU04N0F1OEI0d2cwZ0VyRCtaa00rMDA0ZXNUTHRRSTlFSHU4eDZBMDBwaW5qZmNaaXl0ZHlKSTBQdlFiVFlYNEs5aTlRMzBUMHRtWkI4OEF5SXh0eTQra2IzNjRCTDdJK0hhZXorcGZZYmxidGZWWFdTaE1pSy9vMmREaHpySEh6RnZUV1pWRFFGa2JVdEVJMk03VUFGS3Fqa2RTakI4QStIWG9iTTQ4VUd6UWl5U2oyRCtVbk45elhtVW8valRsc2ErNU5ya0x5Q25KRHZvUm5FRWtlMlA0NjErVnZTcTlFMm43TmdoVW0zU2ZhemkwZC90T0JKaVprWUZONC8xWXpZT3JkOHRjM0l5dmRMaDU1QnFRa0NLMU5YTUhDdTZnTHRtRGJIYnp2T1pBMkY1ODFleEdIRmhkVExYT2hMT24iLCJtYWMiOiI3Njk1YWJhMzg4NDZlMDFhN2Q1YWY1ZGM4NjEwZThhZGEwODhmMzJkOGRmY2JiOTQwZTA0NzA3Yjg3YTQzN2MzIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:33:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFvM1pkU2QvYkNmV0pXTFY5aWk3MWc9PSIsInZhbHVlIjoiTXhRNGlaRGd1QXZGRHFWTHBhclJRMUF3QUZyY0tWM3FROFpnSXBPcU1aY2RpdXZwRnZQYUFERkplZytxZWhBMks0S0lndm1Ya01NVUMxU0RmZmE5b3dxRDRHdXZib09wbS8yWVYzZWova1JwZjNSZkNuTTZZeG04dkpLaFFRdzYrMElScjA4Mm95dlZHK2Uvb0dYVTd6bFRjMkJRc2xMZVBZVWtvd1F2b1gvS0Y2QndiaU03MkdUcnhsank1TExHcUFoeFpTWjFaR1ZFdERtKzRrTXNRSHg1NlBlMFdwRHY5TlljLzJPN2dDc0ZNUjVhdnFlcW16eTZoa09kMG1MckQxZEdaUll3TWlwTFVpVFdKM2FHaFRYcXBicDhWOE5ZS1ZwU1Fob0pnMTBzNUhPWmRhY2J5bDJOSUlxWURoT0RJdExXQlkrWmpqVzNiaVp0Y1R1cCtYUDBpRWFvbWxHam1LQTdWV09LbnAxYWhCM2tYUlZJMktpSWVyTEF4Mk0xMWFaeC9CNDdjNUVmZjk2akIwMXVkREJrZkRRY09ta3dINjJvWmlheENzd1ZwQmhVbi91RTAwYlNXamNiTzg1VkwreTBnTFkxcHJYV0FkcDF3WW9qVDFKRW5QNXJrbjZXOXZtUG9PU04yOU1JZ0N0djZtTlJqVFZHaFZzZDAyMnIiLCJtYWMiOiJlMDBiYmE1ZTZiOTI1YmFiMDhkZTMzNThiYzUzZDlkOTE2MzgwNmFlN2M0ZDUzZWZhMzJlNjFkOTliM2VlZjRiIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:33:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkRBak5mdUtzRWFadGxKUk1VM3k2QXc9PSIsInZhbHVlIjoiK2gvaHkzWEhvYnRFelhRQnJOTVVMRTRCdFJoZTZlVDJYWWRndmZWejAySWJYQnlRZjRhcHVrNk5mdWdQNHFHdnRPZ3NoYmJxWEtucjMyNGpvY3hVTE54TmxTWXVsUVJXRXJCSFl3ZjlNZ1IvLzVTS3FoMWk1SDZEaGJCWnlsY3RsZmdmOGZxK3pTRmJUVWFORTRiSWZtZkM2MzZ5SmhYWENMTTRVc3FmM1QyMWQ3ZURLMTZkTHk2NzVDOE1TVm9tYU04N0F1OEI0d2cwZ0VyRCtaa00rMDA0ZXNUTHRRSTlFSHU4eDZBMDBwaW5qZmNaaXl0ZHlKSTBQdlFiVFlYNEs5aTlRMzBUMHRtWkI4OEF5SXh0eTQra2IzNjRCTDdJK0hhZXorcGZZYmxidGZWWFdTaE1pSy9vMmREaHpySEh6RnZUV1pWRFFGa2JVdEVJMk03VUFGS3Fqa2RTakI4QStIWG9iTTQ4VUd6UWl5U2oyRCtVbk45elhtVW8valRsc2ErNU5ya0x5Q25KRHZvUm5FRWtlMlA0NjErVnZTcTlFMm43TmdoVW0zU2ZhemkwZC90T0JKaVprWUZONC8xWXpZT3JkOHRjM0l5dmRMaDU1QnFRa0NLMU5YTUhDdTZnTHRtRGJIYnp2T1pBMkY1ODFleEdIRmhkVExYT2hMT24iLCJtYWMiOiI3Njk1YWJhMzg4NDZlMDFhN2Q1YWY1ZGM4NjEwZThhZGEwODhmMzJkOGRmY2JiOTQwZTA0NzA3Yjg3YTQzN2MzIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:33:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1933709188\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-384819080 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-384819080\", {\"maxDepth\":0})</script>\n"}}