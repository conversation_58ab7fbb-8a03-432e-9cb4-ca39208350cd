{"__meta": {"id": "X0de5a67bff648e03a378af91b668968d", "datetime": "2025-07-30 09:57:25", "utime": **********.633619, "method": "GET", "uri": "/finance/sales/contacts/search?search=pa", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753869444.68302, "end": **********.633642, "duration": 0.9506218433380127, "duration_str": "951ms", "measures": [{"label": "Booting", "start": 1753869444.68302, "relative_start": 0, "end": **********.497861, "relative_end": **********.497861, "duration": 0.8148407936096191, "duration_str": "815ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.497899, "relative_start": 0.8148789405822754, "end": **********.633644, "relative_end": 2.1457672119140625e-06, "duration": 0.13574504852294922, "duration_str": "136ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46688328, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=925\" onclick=\"\">app/Http/Controllers/FinanceController.php:925-992</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.013269999999999999, "accumulated_duration_str": "13.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.576109, "duration": 0.006019999999999999, "duration_str": "6.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 45.365}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5992181, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 45.365, "width_percent": 7.611}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%pa%' or `email` like '%pa%' or `contact` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 941}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.605726, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:941", "source": "app/Http/Controllers/FinanceController.php:941", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=941", "ajax": false, "filename": "FinanceController.php", "line": "941"}, "connection": "radhe_same", "start_percent": 52.977, "width_percent": 26.074}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%pa%' or `email` like '%pa%' or `phone` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 965}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6151981, "duration": 0.00278, "duration_str": "2.78ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:965", "source": "app/Http/Controllers/FinanceController.php:965", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=965", "ajax": false, "filename": "FinanceController.php", "line": "965"}, "connection": "radhe_same", "start_percent": 79.05, "width_percent": 20.95}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-2140258557 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2140258557\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1348309088 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">pa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1348309088\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-230656117 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-230656117\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1793226526 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6InpRMnh3SHZMa0FzSFJ5dGRYcXJtUmc9PSIsInZhbHVlIjoiNFZLWWk5RjQ3T05NSi9xaGxTRDlZS25Wd1lmVktyeGtpTjcweWF0ajlheGpVdDR6Y0tNT3J3THlORXN0Y0tHd1VNZ3BKNHNLWnhveGRvQldRa3JwcktLNUt6MDI5V0hTWVRaakpNaGRUWkVmVnZiQm5kZmhCOHV4TUlSZ2NQR2Z6NmZhYXAwNlljUHhUc1Z0NVk2SEhoUWRoRitrd3Mrb2R4MVRUU1l5M3R4RUt2Z0tnT0dZdHluK3BuNGsydzd5RDVYbHRQTkUxZm1oTDdUWWtQb2pqbnBDYnJTM2tDNWRoQ2t4TjQvVUlrVldWaGZHNXU0L0VSMkE3K0lVbzFUSkNCdHJOR1RJWU9PMGFZNWtCczRJT29mOENPeTdyZ2w1SzJoVHFIOUFwSU5vMkVTbG5pNTF6MVc2eFVBWi9maDdTVS9BSlhaSlVQY2VwY1hOYkozUm9GMTM3U3JpVGx2ZEVhNUxVT0lFNVQyTFFicjJqMS9hSjczME1lM2dTWTRKRStIR0NWSi9NaEFpM2hUdVNGd1VZWGJyeWxwS2JxWmVqcVkxVUprbFRxSGVXaTR1RS9seU9UcVRDM2tjNys3ODNLTm9vd1NpMWlvekhmV1pPU3Z0UVNZTVBKeldBQkFvdjE4cXhVcGNPYU54dGZCWXRVbGVyMm02WFNCOWxkZWgiLCJtYWMiOiJkNjUxNGNmYjA2MGQ5MDhmMjhkMTI1Y2U0NDRkNmE4ZTk2MThkZDNlMzg0OWRlNzEwYTg4NGYxMDMwYjgwMTlhIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ikh0bklkdU9vSDZVK1NPRnYvaGhnUWc9PSIsInZhbHVlIjoiM3JjWWpZWEZTMW9uMURUVWJVaXUxWUxvcnFrSHpXbXpqdTZlNzZ1U1IyWTU4dTV5SHpaNEVLR1R1T1NmejZMVnZ3c01iZzBjOFA4czlwcmpSZWVPZVVXMlFIWDZLOXpPcUZwR05DWUQzNEZoeS83dG9ZMWV0Q01BZWs4bWprbjZ3akphWjY0VGxiWGlTMDEvbFN5WnBRNmVsTHRHOVBady9NOWVRVFZQT3cyc21weU9kdnZUVHRML05JcnQ2ODVkdklDb2ZpMC9NQ0tWNVNuSjl6b2VDVVBlcFF2UjRFNDN4NFhCdUVOL2JLSURpeHlhd1pPRXhnTWZpbzdPRUJHQno4THRLWkNTZllieEI3NEorM3BVLzg5Vk5udE9HQ3ZLN3dGQ0QrZkJhOEtqZ3YwdFRQbVIwQ0M5RE5tQmtxY1ltVzJqSFVEYVBnTUQ4TGVrZE51eWlhckJLQk9KK292OC9yTU9VTWlHTFVLY2doN25FL2dZS0Zhbm5BcWNFOGZrRVZsK0xOUUoya3cwb09mUjFvVjRGSklreTVraW5FS2NtTHpZWkh5eVlVRnEwZGszTnpCRGZBYnhYZkp5NkxIRm55aDlaVmR4MW42N3orTW83cTlSL2E0L3RHWWc3bU9zV1dSWm53bzh6bnlER3FnQ3FjVVc0NXZjVHduREJKR1kiLCJtYWMiOiI0MDA1M2Y0MTczOGFiMDI2MWMxMzYxMzdlNDMyZTJjODdjMWRhZGY3ZTI5ZmExNmY0OTljNzM5MjRmMDAwZWI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1793226526\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-861508322 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-861508322\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1032797991 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 09:57:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjF2R2MyU01SYXN4R1pPaDd3cUQwd2c9PSIsInZhbHVlIjoiK2VaVWpaRHJpVUViTThZNUsrWkkwa3BvM0plZ04yOUpoeXFLaG9PeDZhWFE5aG9wZzkxN3FaUkR5d1pETkovQ2p2emNrWDNna3IxY1Ruc1NBUFB4amREYSt2RnkzZmwrblI5NDFuU202Q2VtVUptK2xlbmhMRm1uTWV4V0dyeXlkdTB6b2FucE1uNURsYkNEWEwvcHpocXNyUTIwa3VNMC9yZ01ZRFFVYXNjcnVUemtETXdlTEptNy9GVERsZ3lHbGFabU9ybnVSU1kvR0dKUk1KK1VZdURsN3JWT2JYSm1LMXA3cDBIM0RiTjB2R3E4Z3R1bDFvNlVQM3k2SjR5Y1N0dDZURktTODg3SUtOR0dTUE5vN2F6dTFGc3Rtb2lRbTgyT1E1S0RCT2ZBZnYvOURKdlNYSnpObTlEWm9zbnFOTVhENTdsampnQ0plRFo3L01pVHZ2OWQ4RnFyd1ZQd2xvNko1OEZobGJXZVFsbjVvK21EWmlVTkVxSEdvbTJnUnVyU3Rramw2ZzRlSFBMQkQ0NTB0T2grbVZnenR3Z3pOR3ZnTU5XVlluYmV0T0ZaWXhyZDE1ajNUYkI2QVZkdVNYclpwelFqOCtOM0pnNDh6SXdzaUtpaUorTW1ocmFmSWNCR3d4VjN0cnVJQjl2eFNNdlNCaVdBV0pMNWRwL1QiLCJtYWMiOiJmYmVjOWM3NmE3M2MwOGYxMDQ3NjIyNGY4NjI0ZWUyYmJhOWM4NDllY2FiOWFhNzY1ZTg2NmU4OWQwZWQzZmYzIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:57:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImZGeHNHOTUzNm9xODNpUDh4TGY1dUE9PSIsInZhbHVlIjoiWUFsNTVyUklNZUhBMndOVVJkaWlRQkRhZFFXYVVyR21DQVk2dUdoQnBiMVZBcWF0LzZOYWxkRWFGdUh3YXdPVy9PdEIxeURQVFNGRXZWOWZDREF2M0VWVDd2NjlnakIzZzEwVWVQSlNPTzBJeUpaZmVVaFN0S204RXlTMUlqaDBpSEI5VUF6V3hLdkQ2UmhVQ09PL2RCNXhVZ3RxRDVHRnZJOGsyNUliWmFpWnAxK3pCb1BIUXg4ejVrNlBwK2dzejBmQ0NSUTZOc1ZtMldzTE9RRUo3T2xoV3h6aXpCNE8vdHFhSUxoTDR3em5UcURUbGlBOCtvanp2V0F5SnlvWjd0bXU4Mk5MMWVvR09JaVg0MlREN2Z2M01mTUFGNVRseXZJUG93WkhOQUFPckh2TFl6T2svcUZEWjJyaG5DU21WcWY1RXlWSXgwRjFYQzd0YVo1RDZ5anpBNFJmZEVQUSt1bVN4Q1Y5V2k1WnRmOTBVU25RSmJwbFNOU0M0eWdZcGYxQnNGMDgxYUFzZGpjaUVvcFNleWtzR2Z0TThMdG1mRnFNTTAvdVpyZEg0c09JSm1CZ2FwWE9QMXJ4TFY5Um5vVWN2WlhrUGw1NjF0V0t4eVJrUFZhTXRYT0FuazV3aGIyaXAyZHZzQWVkRFEwS1o4QlVFTldBRHJjVEEvVWkiLCJtYWMiOiI2YjNjNGRkMzAyM2RlNzZiMzk0N2EyMzIzZTVmMjliZjYyMzM1OTYxNmQ3YTgwNzM1YjY3N2FhZDc4ZTMyZWU3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 11:57:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjF2R2MyU01SYXN4R1pPaDd3cUQwd2c9PSIsInZhbHVlIjoiK2VaVWpaRHJpVUViTThZNUsrWkkwa3BvM0plZ04yOUpoeXFLaG9PeDZhWFE5aG9wZzkxN3FaUkR5d1pETkovQ2p2emNrWDNna3IxY1Ruc1NBUFB4amREYSt2RnkzZmwrblI5NDFuU202Q2VtVUptK2xlbmhMRm1uTWV4V0dyeXlkdTB6b2FucE1uNURsYkNEWEwvcHpocXNyUTIwa3VNMC9yZ01ZRFFVYXNjcnVUemtETXdlTEptNy9GVERsZ3lHbGFabU9ybnVSU1kvR0dKUk1KK1VZdURsN3JWT2JYSm1LMXA3cDBIM0RiTjB2R3E4Z3R1bDFvNlVQM3k2SjR5Y1N0dDZURktTODg3SUtOR0dTUE5vN2F6dTFGc3Rtb2lRbTgyT1E1S0RCT2ZBZnYvOURKdlNYSnpObTlEWm9zbnFOTVhENTdsampnQ0plRFo3L01pVHZ2OWQ4RnFyd1ZQd2xvNko1OEZobGJXZVFsbjVvK21EWmlVTkVxSEdvbTJnUnVyU3Rramw2ZzRlSFBMQkQ0NTB0T2grbVZnenR3Z3pOR3ZnTU5XVlluYmV0T0ZaWXhyZDE1ajNUYkI2QVZkdVNYclpwelFqOCtOM0pnNDh6SXdzaUtpaUorTW1ocmFmSWNCR3d4VjN0cnVJQjl2eFNNdlNCaVdBV0pMNWRwL1QiLCJtYWMiOiJmYmVjOWM3NmE3M2MwOGYxMDQ3NjIyNGY4NjI0ZWUyYmJhOWM4NDllY2FiOWFhNzY1ZTg2NmU4OWQwZWQzZmYzIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:57:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImZGeHNHOTUzNm9xODNpUDh4TGY1dUE9PSIsInZhbHVlIjoiWUFsNTVyUklNZUhBMndOVVJkaWlRQkRhZFFXYVVyR21DQVk2dUdoQnBiMVZBcWF0LzZOYWxkRWFGdUh3YXdPVy9PdEIxeURQVFNGRXZWOWZDREF2M0VWVDd2NjlnakIzZzEwVWVQSlNPTzBJeUpaZmVVaFN0S204RXlTMUlqaDBpSEI5VUF6V3hLdkQ2UmhVQ09PL2RCNXhVZ3RxRDVHRnZJOGsyNUliWmFpWnAxK3pCb1BIUXg4ejVrNlBwK2dzejBmQ0NSUTZOc1ZtMldzTE9RRUo3T2xoV3h6aXpCNE8vdHFhSUxoTDR3em5UcURUbGlBOCtvanp2V0F5SnlvWjd0bXU4Mk5MMWVvR09JaVg0MlREN2Z2M01mTUFGNVRseXZJUG93WkhOQUFPckh2TFl6T2svcUZEWjJyaG5DU21WcWY1RXlWSXgwRjFYQzd0YVo1RDZ5anpBNFJmZEVQUSt1bVN4Q1Y5V2k1WnRmOTBVU25RSmJwbFNOU0M0eWdZcGYxQnNGMDgxYUFzZGpjaUVvcFNleWtzR2Z0TThMdG1mRnFNTTAvdVpyZEg0c09JSm1CZ2FwWE9QMXJ4TFY5Um5vVWN2WlhrUGw1NjF0V0t4eVJrUFZhTXRYT0FuazV3aGIyaXAyZHZzQWVkRFEwS1o4QlVFTldBRHJjVEEvVWkiLCJtYWMiOiI2YjNjNGRkMzAyM2RlNzZiMzk0N2EyMzIzZTVmMjliZjYyMzM1OTYxNmQ3YTgwNzM1YjY3N2FhZDc4ZTMyZWU3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 11:57:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1032797991\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1399932427 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1399932427\", {\"maxDepth\":0})</script>\n"}}