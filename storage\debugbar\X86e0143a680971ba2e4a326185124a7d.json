{"__meta": {"id": "X86e0143a680971ba2e4a326185124a7d", "datetime": "2025-07-30 10:08:10", "utime": **********.884767, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753870089.766885, "end": **********.88481, "duration": 1.1179249286651611, "duration_str": "1.12s", "measures": [{"label": "Booting", "start": 1753870089.766885, "relative_start": 0, "end": **********.785531, "relative_end": **********.785531, "duration": 1.018646001815796, "duration_str": "1.02s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.785557, "relative_start": 1.***************, "end": **********.884812, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "99.26ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3038\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1864 to 1870\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1864\" onclick=\"\">routes/web.php:1864-1870</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "HatrpHSF80NfhIey8OUYYT4CKLYDmoUErf77QE06", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1718598306 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1718598306\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-546645012 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-546645012\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1535516871 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1535516871\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-851372262 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-851372262\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-552807467 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-552807467\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-668001208 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 10:08:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFsektBV3NwMGRBZytOSUszR2dpM3c9PSIsInZhbHVlIjoiZHUya05SK1JhZEtqZU9mckVCSXlqMW8wdDRCZUxWUmtwbDI0TWVuZGh2eVg2SXhEZW56QlRlTXI1TW5SemVMdVFmOVZVcEN0WUpkTjlMeUhWU2NOZm92Ny9VOVdLbDVuTkdBNkJtT2czUkQ2THVlM3piVTAwYld1cjhTVkxRbzNXN1FMZzloN0M5MGIzblNDci83ZVFOVUJ1Z3NmVkxLelJkVjByWGp5OFJqR0QwQ1VCcjNWUDczZGFwTGVPekxvcDVOWjFzbDFSV05Nc1JaYjBRd0M0WjJ1TWJSa1UzZmhSc0Z3WnBPK3NxRUJFMGRiMXE3ZXNQN0JJV1dJL2FEYmNDK09SUTVkWDdleksrUk45c2hWQkc5M3JVdXRrNGtGdUVHWUNKVWRFQkdDL2FLMGVOVTBleEpldU52eGxadEEwOGJ0dmVFcjNRZ2lwN2xqSkYyY2szSVNOUnhaSUZKbnRiejhhVnQ1bFNndVZqNS9wTGVrNG14bXdONVJyWTl0WDNZQXR0UEg1RlI1U3drOEE5M0hraTBlQVI3dGpDZCtMd3pac1hOcFJJNFdwbmp1emRBZnZTRU9xNit2ZkR1NXpnK3ZBSksxT0ErQ2Q1blA0aE1zZWc5aEkwZXJLTkV4THNnNFZTOWM2L3V5ZGR3dFhwRkRBU244bmZUZ1JuZlkiLCJtYWMiOiJiMzFlYTNiMDdmYjRjYTY3YzNmZTJkZjQ4MDA4N2UzZWQ1NTViZjdiMzc1OThkYWQ1OWM1YzQ5M2JjYTJkMDFkIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:08:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IktGTUVPMTZ6Z1VUUnV5UWJTMHJmYlE9PSIsInZhbHVlIjoieUluMENmTGdtMUhOOUlxUlo4cXNwaDh6eFR0RnY5a3RhWmsxT1pxdjIxb2xEUWRsb2ZCeWk3bVJWZmNlckYvOUMyVzhjVk1rK0ZNdjRhck5YczZaTVNvNHZUbHl5ZVBvVmVxbUVGSGpuT2ppVXZPdGdJUjhQencrWDA1YWNXTUk4MEk0SUo1V2FaSjJ6dDlFU081UDVkWFlVOGNKV0s0aGgxT2F0QmtrWDUvaDhhdWxBenBlT3IxRFNocmlXMjRLOVdPWmsxbnhsZm9NMDV0b1ViZVBuWEdXdXVSaldIVHVZQWhzdTNvaE9ST1hBNk0xNk52Y2l6blpRRFBnSEtqdmtMU0psNzNnT3VTU3JKSGpYMW45ODB3N081RzM3d3ZGR2QrOEJxV3NhZmtQTEszRTdHTmhpYWh5eGxXZDhlbjg0RG4yNlZyQVp0YksxQ3ptSUhNQ0hOYzJ0YWx6Y25NTE13Tnc2WVpFdlNhbnBoQjFEVjhCcmF3QVNGdEN2TUtueHR4VnNxZ0Qzd0Q0cnViRk1ydUtLZkVndm5MVHN5d3MzZFFUdnVDaVkxdENCbnJXWFNoSGpCcmZ2YlZJNzhLVEQ3QTI4ZytOVDM1SXRUQnJ6QTRVM3NsbXM2YndsdHRBVXQ4NDFLWFJmTjkrQWcvWVBGeUw4bXNZNGFsaEM5SXkiLCJtYWMiOiI2N2Y1OWEyNzJmMWFjOWMzNWUwZjA2NDg0ZDI4OWMwMjhkZTgyZmFiY2E0Nzk1ZDA3ZmQyYWM2Nzg5MTRkNWRlIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:08:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFsektBV3NwMGRBZytOSUszR2dpM3c9PSIsInZhbHVlIjoiZHUya05SK1JhZEtqZU9mckVCSXlqMW8wdDRCZUxWUmtwbDI0TWVuZGh2eVg2SXhEZW56QlRlTXI1TW5SemVMdVFmOVZVcEN0WUpkTjlMeUhWU2NOZm92Ny9VOVdLbDVuTkdBNkJtT2czUkQ2THVlM3piVTAwYld1cjhTVkxRbzNXN1FMZzloN0M5MGIzblNDci83ZVFOVUJ1Z3NmVkxLelJkVjByWGp5OFJqR0QwQ1VCcjNWUDczZGFwTGVPekxvcDVOWjFzbDFSV05Nc1JaYjBRd0M0WjJ1TWJSa1UzZmhSc0Z3WnBPK3NxRUJFMGRiMXE3ZXNQN0JJV1dJL2FEYmNDK09SUTVkWDdleksrUk45c2hWQkc5M3JVdXRrNGtGdUVHWUNKVWRFQkdDL2FLMGVOVTBleEpldU52eGxadEEwOGJ0dmVFcjNRZ2lwN2xqSkYyY2szSVNOUnhaSUZKbnRiejhhVnQ1bFNndVZqNS9wTGVrNG14bXdONVJyWTl0WDNZQXR0UEg1RlI1U3drOEE5M0hraTBlQVI3dGpDZCtMd3pac1hOcFJJNFdwbmp1emRBZnZTRU9xNit2ZkR1NXpnK3ZBSksxT0ErQ2Q1blA0aE1zZWc5aEkwZXJLTkV4THNnNFZTOWM2L3V5ZGR3dFhwRkRBU244bmZUZ1JuZlkiLCJtYWMiOiJiMzFlYTNiMDdmYjRjYTY3YzNmZTJkZjQ4MDA4N2UzZWQ1NTViZjdiMzc1OThkYWQ1OWM1YzQ5M2JjYTJkMDFkIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:08:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IktGTUVPMTZ6Z1VUUnV5UWJTMHJmYlE9PSIsInZhbHVlIjoieUluMENmTGdtMUhOOUlxUlo4cXNwaDh6eFR0RnY5a3RhWmsxT1pxdjIxb2xEUWRsb2ZCeWk3bVJWZmNlckYvOUMyVzhjVk1rK0ZNdjRhck5YczZaTVNvNHZUbHl5ZVBvVmVxbUVGSGpuT2ppVXZPdGdJUjhQencrWDA1YWNXTUk4MEk0SUo1V2FaSjJ6dDlFU081UDVkWFlVOGNKV0s0aGgxT2F0QmtrWDUvaDhhdWxBenBlT3IxRFNocmlXMjRLOVdPWmsxbnhsZm9NMDV0b1ViZVBuWEdXdXVSaldIVHVZQWhzdTNvaE9ST1hBNk0xNk52Y2l6blpRRFBnSEtqdmtMU0psNzNnT3VTU3JKSGpYMW45ODB3N081RzM3d3ZGR2QrOEJxV3NhZmtQTEszRTdHTmhpYWh5eGxXZDhlbjg0RG4yNlZyQVp0YksxQ3ptSUhNQ0hOYzJ0YWx6Y25NTE13Tnc2WVpFdlNhbnBoQjFEVjhCcmF3QVNGdEN2TUtueHR4VnNxZ0Qzd0Q0cnViRk1ydUtLZkVndm5MVHN5d3MzZFFUdnVDaVkxdENCbnJXWFNoSGpCcmZ2YlZJNzhLVEQ3QTI4ZytOVDM1SXRUQnJ6QTRVM3NsbXM2YndsdHRBVXQ4NDFLWFJmTjkrQWcvWVBGeUw4bXNZNGFsaEM5SXkiLCJtYWMiOiI2N2Y1OWEyNzJmMWFjOWMzNWUwZjA2NDg0ZDI4OWMwMjhkZTgyZmFiY2E0Nzk1ZDA3ZmQyYWM2Nzg5MTRkNWRlIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:08:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-668001208\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-174615895 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HatrpHSF80NfhIey8OUYYT4CKLYDmoUErf77QE06</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-174615895\", {\"maxDepth\":0})</script>\n"}}