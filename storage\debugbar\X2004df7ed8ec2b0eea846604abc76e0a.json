{"__meta": {"id": "X2004df7ed8ec2b0eea846604abc76e0a", "datetime": "2025-07-30 10:31:44", "utime": **********.619183, "method": "GET", "uri": "/omx-new-saas/login", "ip": "::1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753871503.07225, "end": **********.619224, "duration": 1.5469741821289062, "duration_str": "1.55s", "measures": [{"label": "Booting", "start": 1753871503.07225, "relative_start": 0, "end": **********.391605, "relative_end": **********.391605, "duration": 1.3193550109863281, "duration_str": "1.32s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.391632, "relative_start": 1.3193821907043457, "end": **********.619243, "relative_end": 1.8835067749023438e-05, "duration": 0.22761082649230957, "duration_str": "228ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46666984, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.547055, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.564167, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.605774, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=263\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:263-278</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.02261, "accumulated_duration_str": "22.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 545}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 267}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4810019, "duration": 0.00917, "duration_str": "9.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 40.557}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'radhe_same' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 527}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 270}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.496591, "duration": 0.00858, "duration_str": "8.58ms", "memory": 0, "memory_str": null, "filename": "Utility.php:527", "source": "app/Models/Utility.php:527", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=527", "ajax": false, "filename": "Utility.php", "line": "527"}, "connection": "radhe_same", "start_percent": 40.557, "width_percent": 37.948}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 533}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 270}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.513762, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Utility.php:533", "source": "app/Models/Utility.php:533", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=533", "ajax": false, "filename": "Utility.php", "line": "533"}, "connection": "radhe_same", "start_percent": 78.505, "width_percent": 3.273}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.548358, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 81.778, "width_percent": 3.715}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.565226, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 85.493, "width_percent": 2.963}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3940}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3988}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.586282, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3940", "source": "app/Models/Utility.php:3940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3940", "ajax": false, "filename": "Utility.php", "line": "3940"}, "connection": "radhe_same", "start_percent": 88.456, "width_percent": 5.219}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3943}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3988}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 9}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.5964732, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Utility.php:3943", "source": "app/Models/Utility.php:3943", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3943", "ajax": false, "filename": "Utility.php", "line": "3943"}, "connection": "radhe_same", "start_percent": 93.675, "width_percent": 3.538}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.600191, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 97.214, "width_percent": 2.786}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "5bAS6pxlTjQQmoANE7jULrxXjNgNlmc1m9mk4gE8", "url": "array:1 [\n  \"intended\" => \"http://localhost/omx-new-saas/finance/sales\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/omx-new-saas/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-457294749 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-457294749\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-133665790 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-133665790\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-822582204 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-822582204\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1067301185 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IlYvc2lycjIzRDlSUUluOEJOa0NqUHc9PSIsInZhbHVlIjoiNFFuN00rKzI2cjFGL3ZzUHF5VklhUUkzYk5HUUVsSmtBU2ZKVjhCOWxUS3BLT2NMeG0rNlJ5MDlrZ2JYYXhxbjZRdms2WlJ2cEIyeFBTcTRYd25SZ0w1OEVEREd0UjdsUFFCYVdOVm5uazhkbVdxUjdzWlVIcEg2VHBZak5LSTBJci8zQzBXd09CVDFaLzFwbjFXR2dGb1RTT041V2ZqaVoyUGkrV1oxa1JLZGwwK2FIUXJ6UERYZjIxSXF0OUFGTVRKTmwyMGZYUWpOR1VheDEyMXJoK3JOL1ZDRnU3cTU5enRsQ3JkZnRHVzhIcS9Ca0tabnZ4L3QycGlxNmdMSklPSmp4OXhNSVh3QmdtcHo3NDdUM2xUcy9lOVVZUWhRSjQ3S21BOFAwbFgrZ3duMmczeEFHbk80R0ZXVUtId1dqcVpVb1RHaE1QYWJXWHRrS0xrYW9zMUpzcUhJZGZmWE85dEowcEl6N01wUnJsYTEyamxsL0t6aUF1TCszUkxnZVR4V2JSSVVTWm5seU9PRGNlV3h5aWRZZ0k5Y2NRZEtnSVZrdjVEZm85c1NqdFk2RTJ1KzJhNEQrUURmdm5TTFZMQmFEdlBuNk9vdjFXVldrazF3aklJWjhtV0pONkc3Ymx3aWhuNWFGaWNrNlR6VUZWZFpXdzNBYVVPWTZFU20iLCJtYWMiOiJmYzM4YzJhNzJjMDY2NjJiOWRlODQ1NWE2Y2NkMjcyYjc2NDk0ZDZkYjNhNzYwOGViMTVkYTZkN2RmZGVhNTdlIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlpieVFCT2dwSkUxU2tZdFhPNTBraVE9PSIsInZhbHVlIjoiYTBzekQ5akdiWkxJQlVoUUxDUGdGeWU3U0VNTVAyZEk2b295b1BYTFd5Qi9TR1NMeGp5K29PZlo5aGduUDVwakc1ZkJ5ZHdDTXRjSGdPUkh1cUsxWkNiYit0YVJ4TzBOQk51ZzFrM0J5UnFLb1ZoQWEveVBoNzBDUjEwdU1WbjN2cytJVTlhWGQ0RjB2dloxd2FaTXFWR1E2RTN0aHJiWktoZHJ6TmRaelBxMmcyOCt2elp3dHNadDBiekNqbldYdzBkSEY2ZzlqVXA0ejJ1UUlFSXNxd2tzcDQ0S0V4WkxqVWlCK1Rqazhlc240cDFManErVm5zSnJjNUU2Q1c4WGpGaVFFTWRTWHhrbzNHbnBQRGNvelpycEczdnB2bjJRMjh2RVFNNDRseEltVVR3WndUYy9GcHg0TzRuZkd1TUpQWi9RWlNjaDFlcDM3WEZrTk5BcWIvWk1vZVpJUHZ1U3hZVXhQTTQraml0VjlVS1JQUk9VTFBhalp4VHFhenpqcm5uMWYyaHR1cHZhcGovL1plQklxczN0UXNYb2dyYUdEbjBGeGl1NDA1QkdJR3RQMFBUOGtQZXBoR0FBK1JIRmdPaklEV1MwWDA3Y24xcEd2U2JDemtBeHRWeWZsWTM1VlVXQi96YmZ4TmRQeUZsMCthUi92QUFRWlNIVlI0OTMiLCJtYWMiOiJkMGVhOGI2ZDJkY2FhMzkwOGU5ODA0OWNjMTJjNDgyNjlmYjNiNGI3Y2FlNTY0NDc5ZmEyZDQxNmYwNmMwNWY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1067301185\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1070080854 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5bAS6pxlTjQQmoANE7jULrxXjNgNlmc1m9mk4gE8</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7f4EUZP93e4zKShJdjMEQKdIg0VLTOjvnrPmQ2oa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1070080854\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-512207841 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 10:31:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iml5QXFKSWVGN0l3dEJOUGMxbG8vdmc9PSIsInZhbHVlIjoiaStMQi9hblovNHowUnVjZ2lTQ2hjNWJ2dGQ2U3M1YWM0SWRLMUs3bk95dnFpc1dSN0FFL1Z2UlpsRkg4TnlQOUF6NmYwQVNJMTJQZkxXM2o3TDlFQXI0YWdFMklvQlYrTkZzdHhZYnlrNWQrNEZ5NnR0Z3FNWFlhQk9BZTRBekhaTElJQ3NzTHdXV0NpN1dQbSs4YWpTWW1TeWFXYncxcHVMaE9OcEdWWWNUMkdCNld6M2h4T0ZWVWtTUVllR3lkRUNuSTJreTVsc3FMT2tJVGMyY3dLaHFqOU9PYk8rRElMKytxWEVLVEFqcW52VndrV3RKZ1RLUjViaTdwR0NhUlFvYjZyWWhSVjA1QjYyQ1pWVWowKzZlMHFRNjZUeGhicExvYXQ1cGdDRDd3OXZGQWxIV2ltb1hxWFJFT3V0eVJDZmt0YjVUeGVObkdSMXpZS25MTUR1RzBIM1V1dC80V3QvVHhBUGZMYWxDMnNGYkVnMjZlWUowU0JiSDdDOGRJS0E3cVJHMUVYS2REd3FyVmIyeEZHY09XeklRODlqdUI1VnRuYXdVTGxCNDFTblNUcnVUVDRuRFowOG9MeVlZc0kxT3B2UnVENUlXWFJsTWRxMy9QZ0NiS1kydHBBRG5saUI5NmxjTFhBU2s1ZmgxUU5qRDVIZFJYTjNSQ0tHREciLCJtYWMiOiIzMzg3NmNhMTI1YjBlNzBkNTMyYzBiMGIwNmEyNDI1YWNkOWVhODkwOTUwNGVjZDI0ZWQ4ZTBkMDhkMGI5ODExIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:31:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik50UWE3UVJpS3ZZaGt5VXpud3cvNFE9PSIsInZhbHVlIjoidnFuRzZ3V3hLZnJOckx1MWVMbkVuUlFjbVhzUDBiWDJubmRxdTBGcm9ienRNd1QrR2szYkpUWVVqN3czU05DL3VvdFdYME44Yldqb0FSWlBhZ0pNYzlTTEd5OGtkZzJDN2V1amhaNWp0dFgrbkZ1ZktEYlFuOGFWa1J4aTllcnk5b000Mkx0VW9qRHNQM3BiUG5Oc2tKRElkOUl1UXM4RzhzTzZqQ3pBbWk4TitVRTNobkl3enNDTWFlbFBtVFJLS2xjTDdEWTVoTWpNQkp0Vk40ZzA0YnE5Y2Z1R0lIeUtPYlBXK3VldXlWZWF1YVpLVVJCTGdZckpYYzgwS3JQMkZ5ZmROZG14WVZqY1VhQzdpYkpTelZrenR1NHV6L0pwOG1JNFpTNFBWbDBSTTFRUE9LV0hKdFcrODZaYjNmdU14L3NTMDZCWTdqRnc0YjVMbjM3K3V5NzFDa3EyRTBTalZZdmhlc1J3UDhIb1ZoWTd2d0h1NFV0NkxvejVwcEN2ZjZvT2hNYlVnQnkzL3FvT1l3SmtWUm5oMWY2NG5MNlFuRlNnTVF5QXBRNGtob1BRMzBscUI0OHYwdFRxdjc0b2Qya3BobDNGT0FFZTVZQmt5N2xVd3NOd2J4Ym4rUkhRQmg4WW1kU0NqY1BmYU9yckRoRCs4cGNFbGp4MkRPU04iLCJtYWMiOiJmZWI2ZGE3MmIzOGEyMzQwYTZlMTViYzQzNjRiOWI5YTg1ZTRhZjQzMTYzY2RjNTBjYTdhMDhiNjBlMTQwNDM3IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:31:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iml5QXFKSWVGN0l3dEJOUGMxbG8vdmc9PSIsInZhbHVlIjoiaStMQi9hblovNHowUnVjZ2lTQ2hjNWJ2dGQ2U3M1YWM0SWRLMUs3bk95dnFpc1dSN0FFL1Z2UlpsRkg4TnlQOUF6NmYwQVNJMTJQZkxXM2o3TDlFQXI0YWdFMklvQlYrTkZzdHhZYnlrNWQrNEZ5NnR0Z3FNWFlhQk9BZTRBekhaTElJQ3NzTHdXV0NpN1dQbSs4YWpTWW1TeWFXYncxcHVMaE9OcEdWWWNUMkdCNld6M2h4T0ZWVWtTUVllR3lkRUNuSTJreTVsc3FMT2tJVGMyY3dLaHFqOU9PYk8rRElMKytxWEVLVEFqcW52VndrV3RKZ1RLUjViaTdwR0NhUlFvYjZyWWhSVjA1QjYyQ1pWVWowKzZlMHFRNjZUeGhicExvYXQ1cGdDRDd3OXZGQWxIV2ltb1hxWFJFT3V0eVJDZmt0YjVUeGVObkdSMXpZS25MTUR1RzBIM1V1dC80V3QvVHhBUGZMYWxDMnNGYkVnMjZlWUowU0JiSDdDOGRJS0E3cVJHMUVYS2REd3FyVmIyeEZHY09XeklRODlqdUI1VnRuYXdVTGxCNDFTblNUcnVUVDRuRFowOG9MeVlZc0kxT3B2UnVENUlXWFJsTWRxMy9QZ0NiS1kydHBBRG5saUI5NmxjTFhBU2s1ZmgxUU5qRDVIZFJYTjNSQ0tHREciLCJtYWMiOiIzMzg3NmNhMTI1YjBlNzBkNTMyYzBiMGIwNmEyNDI1YWNkOWVhODkwOTUwNGVjZDI0ZWQ4ZTBkMDhkMGI5ODExIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:31:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik50UWE3UVJpS3ZZaGt5VXpud3cvNFE9PSIsInZhbHVlIjoidnFuRzZ3V3hLZnJOckx1MWVMbkVuUlFjbVhzUDBiWDJubmRxdTBGcm9ienRNd1QrR2szYkpUWVVqN3czU05DL3VvdFdYME44Yldqb0FSWlBhZ0pNYzlTTEd5OGtkZzJDN2V1amhaNWp0dFgrbkZ1ZktEYlFuOGFWa1J4aTllcnk5b000Mkx0VW9qRHNQM3BiUG5Oc2tKRElkOUl1UXM4RzhzTzZqQ3pBbWk4TitVRTNobkl3enNDTWFlbFBtVFJLS2xjTDdEWTVoTWpNQkp0Vk40ZzA0YnE5Y2Z1R0lIeUtPYlBXK3VldXlWZWF1YVpLVVJCTGdZckpYYzgwS3JQMkZ5ZmROZG14WVZqY1VhQzdpYkpTelZrenR1NHV6L0pwOG1JNFpTNFBWbDBSTTFRUE9LV0hKdFcrODZaYjNmdU14L3NTMDZCWTdqRnc0YjVMbjM3K3V5NzFDa3EyRTBTalZZdmhlc1J3UDhIb1ZoWTd2d0h1NFV0NkxvejVwcEN2ZjZvT2hNYlVnQnkzL3FvT1l3SmtWUm5oMWY2NG5MNlFuRlNnTVF5QXBRNGtob1BRMzBscUI0OHYwdFRxdjc0b2Qya3BobDNGT0FFZTVZQmt5N2xVd3NOd2J4Ym4rUkhRQmg4WW1kU0NqY1BmYU9yckRoRCs4cGNFbGp4MkRPU04iLCJtYWMiOiJmZWI2ZGE3MmIzOGEyMzQwYTZlMTViYzQzNjRiOWI5YTg1ZTRhZjQzMTYzY2RjNTBjYTdhMDhiNjBlMTQwNDM3IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:31:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-512207841\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1815812227 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5bAS6pxlTjQQmoANE7jULrxXjNgNlmc1m9mk4gE8</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://localhost/omx-new-saas/finance/sales</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/omx-new-saas/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1815812227\", {\"maxDepth\":0})</script>\n"}}