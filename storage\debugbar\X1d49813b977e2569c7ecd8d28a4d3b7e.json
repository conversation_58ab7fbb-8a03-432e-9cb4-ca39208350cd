{"__meta": {"id": "X1d49813b977e2569c7ecd8d28a4d3b7e", "datetime": "2025-07-30 10:12:25", "utime": **********.805663, "method": "GET", "uri": "/finance/sales/contacts/search?search=pa", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.010809, "end": **********.805682, "duration": 0.7948729991912842, "duration_str": "795ms", "measures": [{"label": "Booting", "start": **********.010809, "relative_start": 0, "end": **********.675359, "relative_end": **********.675359, "duration": 0.6645500659942627, "duration_str": "665ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.675385, "relative_start": 0.6645760536193848, "end": **********.805685, "relative_end": 3.0994415283203125e-06, "duration": 0.13030004501342773, "duration_str": "130ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46721216, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET finance/sales/contacts/search", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\FinanceController@searchContacts", "namespace": null, "prefix": "/finance", "where": [], "as": "finance.sales.search-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1238\" onclick=\"\">app/Http/Controllers/FinanceController.php:1238-1305</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02104, "accumulated_duration_str": "21.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7421572, "duration": 0.01098, "duration_str": "10.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 52.186}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.766754, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 52.186, "width_percent": 4.468}, {"sql": "select * from `customers` where `created_by` = 79 and `is_active` = 1 and (`name` like '%pa%' or `email` like '%pa%' or `contact` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1254}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.77441, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1254", "source": "app/Http/Controllers/FinanceController.php:1254", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1254", "ajax": false, "filename": "FinanceController.php", "line": "1254"}, "connection": "radhe_same", "start_percent": 56.654, "width_percent": 16.968}, {"sql": "select * from `leads` where `created_by` = 79 and `is_active` = 1 and `is_converted` = 0 and (`name` like '%pa%' or `email` like '%pa%' or `phone` like '%pa%')", "type": "query", "params": [], "bindings": ["79", "1", "0", "%pa%", "%pa%", "%pa%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/FinanceController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\FinanceController.php", "line": 1278}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.784637, "duration": 0.00555, "duration_str": "5.55ms", "memory": 0, "memory_str": null, "filename": "FinanceController.php:1278", "source": "app/Http/Controllers/FinanceController.php:1278", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FFinanceController.php&line=1278", "ajax": false, "filename": "FinanceController.php", "line": "1278"}, "connection": "radhe_same", "start_percent": 73.622, "width_percent": 26.378}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/finance/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/finance/sales/contacts/search", "status_code": "<pre class=sf-dump id=sf-dump-556187215 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-556187215\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1680086442 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => \"<span class=sf-dump-str title=\"2 characters\">pa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1680086442\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-926097554 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-926097554\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-667247471 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjFvRVEvZnJ1aG5OM0RZLzRDUVBveFE9PSIsInZhbHVlIjoiM2VxVEpBN05jWDZoWDJvODJzUHNXb3JUNGdOUi9JNTNEMXJCbWpycHB3cUg1RXlHRFlqQVdwTlptUUViempGWkN2c29UTmd5NjM3QUttOHprL0tqQnJIaG1TSlNyMkoyL3ZTcTJpSUhRU0pDYTlSWVUvYzlMUUVJanI1TVNXTmFxeVFCMmprQzZrZm5mNWo5eVliV0txZWFIOU1EdGczbkxvOW92ZXdyYnI0cTBSYUZOa21CVFpiT2U4bXNVNWVHRm9TeWtwaStIb0dReDNaNGI4RnhnRC96Q2hWaDFiQjREZGxWQlMxcDVUbWVrcGpPZmsrd3hqYzl4ZU1SNVdJRWp6NklONkFRR0VEZS9BOHByTVZiNThtbzY5STE3ZFk5eVl4Z2phNEhETEd3OFlTdzdFck1qRVNzRDlUR0dydWtFRjNJVkwxbXpkZVVuaTZZSC94Y2M3NWpaMnVCU2I2UEk5QVdBUWJSdm93ZFE5dkRRMko3TnpCQTF3Z0x2emZYT1pkZ2U0ZFlUaTJ0QXpIYjcwYkNyMktCRWswUUMxc3BkR2o4dGoweHkveVN1ZDQ5Qno0NTRQeUdwd0FEMzNKcEFIS09tRmJxMXpldVlZSm1EbDFqbmRSYzcrVjA1WjVoZlJSeHBuanNaM0w0TVkxRmRqbFdnY2duaFhuUW51NmoiLCJtYWMiOiIzMGI3MGIxNTc1ZDE0NzBjZDc4YjlkNTMwYTYxMDMxZTI2MWFiYTZhM2U5NTY3OTg0MWM0ZTU1M2Y4MjViMGI1IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InFJVE4vZWJ4Yk1QL1hJY0d6Q1FrQ1E9PSIsInZhbHVlIjoiOVltWVdrZVgwT3p2bmJCSENyNkNpcjNCQkVERGtNQTdyVkpndzVQWFJPaHRxY0JYSVlvZ096OFBZemlRMFFUVEtqOWRMV1QxR0Y2aDIweUhkbVdHRWJlWnh6WGp1ZU5RN3J6T3RvWVc4Y3I3bU5QNUl2bTNENWxkaSsvd2QzYWVmSEp2dDVWN3lveFRnOWdiVzhWSWFIUDlMRytuOFp4UFF6UXZEMUI2bEYwRlc4RzAwSHdockZhdVNUK2lpc2ZWOXZVSzVzVjRqaFJFbUU5akJhMlMzTFdLNWU1b0xrdnhOdWZqNGR2K3cwZUNHSXJidTBIV3c0TmxiN3F5RUY4V2ljT3FIRVBMVHJJYVRNbU4wWDUxU1Z6ajhMQWVPYmUvM2t0Y3VvejNKakhtZGpQT0hKVGtYRHZIWm9KbnJtRUwvS1FnNExYZ2pkeFpKbitnSjdWTGNrWit1VHEzaFd2bU1oR2xsdGpMakV3U3ZXRnZIRlZKN1RpR1N5M0l1RHcyNzFibW4wRDQ0QkRmNnUwcWN6M21XdVZNS3cyMjRjY3dCZ2cvU3ZJeWp3KzFic2tDRFo0RE01MENvTDlIUXdtK2ZrVUYwdkdkRWE3OFZlWVJQb3BVeFNpN204OGI4aGVObG8xNnJ6QnNxUUFCZ0pVcHBzdnhCS3d5Szh3dzJ0Zm0iLCJtYWMiOiIyY2VmZmE5ODYxMGM3OGE2NTFjMDc0YzdlY2M4NWU3YzY1OGE4Zjk5MzRiYzMxOGNlOTczNzI4NWM5ZjJmNWZjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-667247471\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-794783854 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VrOe7z9INBgGqf5G9OuMEA8F2cBuN2T8w2gvsdwi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-794783854\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1726164248 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 30 Jul 2025 10:12:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhmbDZuYjFSSUpwVS9ob3FsbzlvTWc9PSIsInZhbHVlIjoiQm9JRFVycWxQRVEybFJ4dGFWOHVSWkQyWkpycE1tdnhIUnZZVTJMSXlyY1k3VGVqVDNpVTU3T3ozMkhhK2xqWGtpcmQrYUZYNnRmUnI3cnh0NnB6U0U4bmJuOEdOMzJUY21NZVBabE9zOWRHK0hPYjYzKzdXcmJKbUVocUxKRmZFYlhsWTBYcEZUOFhEOWltYWpoZW84SGRKWktEVnZUdXJZTU1FT1B1dFBFcFNKSm91MTM1Tmsxc0RVUHcwSTdJNjIvOHIxb0owNDErdXpXWkc4QVZvOC9ZOTdNRFZINXNUanVCRy9JQmF3RjBqNll0c1hEazFxUkdXK2RaNGFoeVJQNW41TkVMZG1SeDNQVFQ2MEMvTEc0c09BdW9NVXpxdDhSbldlN1FNQ0dhK0xOM0xqanArL29QT0k4WUd5ZUxYL0gyd3piK3R5TmFJbjhVUVUwcC84SzU1S0tDMnhQYXB1S1c2aEo4ZmZCWUJNYWg2ODBQSVFwWHdONFhCVUZodGpReDNmZ201d2wwRVhrNEQ5Z1FuYWVodU1EcDM1WW83MmVtYUk1S3hFREw0dmE3TGJsK25kN2piNDdRbkJMMU85dmUwVEtHREFCYjRGeGtnMVlSa2t3a3NqOXVEVEg1L0lvNlZIZkY2bC8yKzArR3Y4VmNtdkx5QU9YSlc2VXQiLCJtYWMiOiIzMmFjNjZmNDk5OWJmYjE3ODIwMzc4OWMwZWIyYzdhMzA5NDk5YWVmZjk3MzBiZmMxMjZiMjVjYTJkZGY0ZjlmIiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:12:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImQyZ01YUWZWOWJGdU1GS0toaWl0M1E9PSIsInZhbHVlIjoiaS9HMFYyT3Y5RVBWTEZpbHY1eFJqTHpESldROFFlc2FjMG0zY2hVdVp2cDdTRnFTSjlVV1B2SmRiYXhjM2VKelplQnFMYTY1T0h0Rys0cWJ4ZzY4TkdvN1g4bHRpMUlhNUk0MTlUUzdGanl4TFpYRStmdE9oeFMzS2hYSnlaMmU0ekRwSURvSW5GSUlzR1U3VTBjaGZ2VTZ6T2RUK0JmcnJmeGpDTGpNKzJpQnNaL29abmdQNFp4MTE2bzdUTlZQR1dJVFpxQkZSR3kwWGZRNkpIZml1aUxnc0lteXJmeWc2WDVlZ2ZQSWdrMThNMnRsRHdsUDF4VFJQZUZqV1YxYnNmeVBKT0VabS9rcFJydS9tNFFMTUJzUWMwVHZMdk92MnZHaGFSV2dvaFFFNXVVQ3h1NTVaTnkwZE96c2w5QUU2MUtFY3VwbTZqNCtBeWZVOUlyZDBkdnVBd2lpMWtWS2VhTGdKb05pOTBlVGRqdGlrRVlQMms4K1JmbE5uRkVack5zYUdkak8vWUxxTHhac2ZOemlVVC9ndGlqZThKcXRBd1AxcllLSm5uTTBKQk5GZFZEc2U2SUVPeklKOXExNXJRSnEyRWsvVWRISXZTYkFWOCszOWtOYVEwT3RrY1V1UWZJcDVYZHZ2NGRmdXFvdnhTRzJHOHM4bjFuaEo4MFMiLCJtYWMiOiIwMjc4ZTg3MDJjMDk1OTQ4NTcwZjZhNDQyZDZlZGM5YzZjMDA5NGU2MWE1MDBmNDI5MTcxMDFjZjY1YWJjYzI5IiwidGFnIjoiIn0%3D; expires=Wed, 30 Jul 2025 12:12:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhmbDZuYjFSSUpwVS9ob3FsbzlvTWc9PSIsInZhbHVlIjoiQm9JRFVycWxQRVEybFJ4dGFWOHVSWkQyWkpycE1tdnhIUnZZVTJMSXlyY1k3VGVqVDNpVTU3T3ozMkhhK2xqWGtpcmQrYUZYNnRmUnI3cnh0NnB6U0U4bmJuOEdOMzJUY21NZVBabE9zOWRHK0hPYjYzKzdXcmJKbUVocUxKRmZFYlhsWTBYcEZUOFhEOWltYWpoZW84SGRKWktEVnZUdXJZTU1FT1B1dFBFcFNKSm91MTM1Tmsxc0RVUHcwSTdJNjIvOHIxb0owNDErdXpXWkc4QVZvOC9ZOTdNRFZINXNUanVCRy9JQmF3RjBqNll0c1hEazFxUkdXK2RaNGFoeVJQNW41TkVMZG1SeDNQVFQ2MEMvTEc0c09BdW9NVXpxdDhSbldlN1FNQ0dhK0xOM0xqanArL29QT0k4WUd5ZUxYL0gyd3piK3R5TmFJbjhVUVUwcC84SzU1S0tDMnhQYXB1S1c2aEo4ZmZCWUJNYWg2ODBQSVFwWHdONFhCVUZodGpReDNmZ201d2wwRVhrNEQ5Z1FuYWVodU1EcDM1WW83MmVtYUk1S3hFREw0dmE3TGJsK25kN2piNDdRbkJMMU85dmUwVEtHREFCYjRGeGtnMVlSa2t3a3NqOXVEVEg1L0lvNlZIZkY2bC8yKzArR3Y4VmNtdkx5QU9YSlc2VXQiLCJtYWMiOiIzMmFjNjZmNDk5OWJmYjE3ODIwMzc4OWMwZWIyYzdhMzA5NDk5YWVmZjk3MzBiZmMxMjZiMjVjYTJkZGY0ZjlmIiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:12:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImQyZ01YUWZWOWJGdU1GS0toaWl0M1E9PSIsInZhbHVlIjoiaS9HMFYyT3Y5RVBWTEZpbHY1eFJqTHpESldROFFlc2FjMG0zY2hVdVp2cDdTRnFTSjlVV1B2SmRiYXhjM2VKelplQnFMYTY1T0h0Rys0cWJ4ZzY4TkdvN1g4bHRpMUlhNUk0MTlUUzdGanl4TFpYRStmdE9oeFMzS2hYSnlaMmU0ekRwSURvSW5GSUlzR1U3VTBjaGZ2VTZ6T2RUK0JmcnJmeGpDTGpNKzJpQnNaL29abmdQNFp4MTE2bzdUTlZQR1dJVFpxQkZSR3kwWGZRNkpIZml1aUxnc0lteXJmeWc2WDVlZ2ZQSWdrMThNMnRsRHdsUDF4VFJQZUZqV1YxYnNmeVBKT0VabS9rcFJydS9tNFFMTUJzUWMwVHZMdk92MnZHaGFSV2dvaFFFNXVVQ3h1NTVaTnkwZE96c2w5QUU2MUtFY3VwbTZqNCtBeWZVOUlyZDBkdnVBd2lpMWtWS2VhTGdKb05pOTBlVGRqdGlrRVlQMms4K1JmbE5uRkVack5zYUdkak8vWUxxTHhac2ZOemlVVC9ndGlqZThKcXRBd1AxcllLSm5uTTBKQk5GZFZEc2U2SUVPeklKOXExNXJRSnEyRWsvVWRISXZTYkFWOCszOWtOYVEwT3RrY1V1UWZJcDVYZHZ2NGRmdXFvdnhTRzJHOHM4bjFuaEo4MFMiLCJtYWMiOiIwMjc4ZTg3MDJjMDk1OTQ4NTcwZjZhNDQyZDZlZGM5YzZjMDA5NGU2MWE1MDBmNDI5MTcxMDFjZjY1YWJjYzI5IiwidGFnIjoiIn0%3D; expires=Wed, 30-Jul-2025 12:12:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1726164248\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1197656685 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LBvlpNLXINxnwjNI8HggKzcyOoGf4TY68lWPrkFP</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/finance/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1197656685\", {\"maxDepth\":0})</script>\n"}}